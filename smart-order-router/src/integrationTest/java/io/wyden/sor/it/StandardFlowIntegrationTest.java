package io.wyden.sor.it;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class StandardFlowIntegrationTest extends SORIntegrationTestBase {

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldRejectSmartOrderIfCandidatesNotFound(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequest(orderType, "LUNABTC"));
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());

        OemsResponse parentExecRejected = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_REJECTED);
        assertExecReportRejectedFor(parentExecRejected, smartOrder, "No available targets");
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldRejectSmartOrderIfSymbolNotAvailableOnSelectedVenues(OemsOrderType orderType) {
        OemsRequest request = testingData.clientSmartOrderRequest(orderType).toBuilder()
            .clearVenueAccounts()
            .addVenueAccounts("SomeVenueAccountThatDoesntProvideThisInstrument")
            .addVenueAccounts("AnotherVenueAccountThatDoesntProvideThisInstrument")
            .build();
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(request);
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());

        OemsResponse parentExecRejected = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_REJECTED);
        assertExecReportRejectedFor(parentExecRejected, smartOrder, "No available targets");
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void executionReportShouldIncludeTIFAndExpireTime(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequest(orderType));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is filled
        OemsResponse emittedByCollider = collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(response.getRootExecution()).isEqualTo(emittedByCollider.getRootExecution());
            assertThat(response.getUnderlyingExecution()).isEqualTo(OemsResponse.ExecutionReference.newBuilder()
                    .setExecutionId(emittedByCollider.getExecutionId())
                    .setOrderId(emittedByCollider.getOrderId())
                    .build()
            );
            assertThat(response.getTif()).isEqualTo(smartOrder.getTif());
            assertThat(response.getExpireTime()).isEqualTo(smartOrder.getExpireTime());
        });
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldSkipCandidateWithEmptyConstraints(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestNoTradingConstraints(orderType));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                ETHUSD_BITMEX_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDoneNoTradingConstraints(bestExecutionRequest.getRecommendationSubscriptionId());

        // no child order requests
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldExpireSmartOrder(OemsOrderType orderType) {
        ZonedDateTime expireTime = ZonedDateTime.now().plus(2, ChronoUnit.SECONDS);
        String expire = DateUtils.toFixUtcTime(expireTime);
        OemsRequest oemsRequest = testingData.clientSmartOrderRequest(orderType);
        OemsRequest smartOrder = oemsRequest.toBuilder().setExpireTime(expire).build();
        orderGateway.emitsOemsRequest(smartOrder);


        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // parent expired, so child order is canceled
        OemsRequest cancelChildOrder = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(cancelChildOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // parent expired
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_EXPIRED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_EXPIRED);
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getTif()).isEqualTo(smartOrder.getTif());
            assertThat(response.getExpireTime()).isEqualTo(smartOrder.getExpireTime());
        });
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldNotExpireSmartOrderIfChildNotCanceled(OemsOrderType orderType) {
        ZonedDateTime expireTime = ZonedDateTime.now().plus(2, ChronoUnit.SECONDS);
        String expire = DateUtils.toFixUtcTime(expireTime);
        OemsRequest oemsRequest = testingData.clientSmartOrderRequest(orderType);
        OemsRequest smartOrder = oemsRequest.toBuilder().setExpireTime(expire).build();
        orderGateway.emitsOemsRequest(smartOrder);


        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // parent expired, so child order is canceled
        OemsRequest cancelChildOrder = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(cancelChildOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child not reported as canceled, parent does not expire
        collider.ensureNoOrderRequests(smartOrder.getOrderId());
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldExpireSmartOrderIfChildPartiallyFilled(OemsOrderType orderType) {
        ZonedDateTime expireTime = ZonedDateTime.now().plus(2, ChronoUnit.SECONDS);
        String expire = DateUtils.toFixUtcTime(expireTime);
        OemsRequest oemsRequest = testingData.clientSmartOrderRequest(orderType);
        OemsRequest smartOrder = oemsRequest.toBuilder().setExpireTime(expire).build();
        orderGateway.emitsOemsRequest(smartOrder);


        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        collider.emitExecutionReportPartiallyFilled(childOrder);

        // parent expired, so child order is canceled
        OemsRequest cancelChildOrder = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), KRAKEN);
        assertThat(cancelChildOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_KRAKEN_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(KRAKEN);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            
            assertThat(request.getOrderType()).isEqualTo(resolveChildOrderType(orderType));
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getRootOrderId()).isEqualTo(smartOrder.getRootOrderId());
            assertThat(request.getClientRootOrderId()).isEqualTo(smartOrder.getClientRootOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // parent expired
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_EXPIRED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_EXPIRED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getTif()).isEqualTo(smartOrder.getTif());
            assertThat(response.getExpireTime()).isEqualTo(smartOrder.getExpireTime());
        });
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldNotHandleExecutionRecommendationsWhenExpired(OemsOrderType orderType) {
        ZonedDateTime expireTime = ZonedDateTime.now().plus(2, ChronoUnit.SECONDS);
        String expire = DateUtils.toFixUtcTime(expireTime);
        OemsRequest oemsRequest = testingData.clientSmartOrderRequest(orderType);
        OemsRequest smartOrder = oemsRequest.toBuilder().setExpireTime(expire).build();
        orderGateway.emitsOemsRequest(smartOrder);

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // no recommendations yet, SRE sends out heartbeat message, don't send any child orders
        smartRecommendationEngine.emitExecutionRecommendationsProcessing(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // parent expired
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_EXPIRED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_EXPIRED);
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getTif()).isEqualTo(smartOrder.getTif());
            assertThat(response.getExpireTime()).isEqualTo(smartOrder.getExpireTime());
        });

        // recommendations done, but no child order is sent because parent expired
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    public void shouldNotHandleExecutionRecommendationsWhenCanceled(OemsOrderType orderType) throws InterruptedException {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequest(orderType));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_BITMEX_INSTRUMENT_ID, BTCUSD_KRAKEN_INSTRUMENT_ID);

        // no recommendations yet, SRE sends out heartbeat message, don't send any child orders
        smartRecommendationEngine.emitExecutionRecommendationsProcessing(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequest(orderType));

        TimeUnit.SECONDS.sleep(2);

        // recommendations done, but no child order is sent because parent canceled
        smartRecommendationEngine.emitExecutionRecommendationsDone(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }
    
    private static OemsOrderType resolveChildOrderType(OemsOrderType orderType) {
        switch (orderType) {
            case MARKET, STOP: return OemsOrderType.MARKET;
            case LIMIT, STOP_LIMIT: return OemsOrderType.LIMIT;
            default: return OemsOrderType.ORDER_TYPE_UNSPECIFIED;
        }
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldSkipCandidateWithEmptyPriceIncr(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMissingPriceIncr(orderType));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            ADAUSD_BITMEX_MISSING_PRICEINCR_INSTRUMENT_ID);

        smartRecommendationEngine.emitExecutionRecommendationsDoneMissingPriceIncr(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldSkipCandidateWithEmptyQtyIncr(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMissingQtyIncr(orderType));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            EURUSD_BITMEX_MISSING_QTYINCR_INSTRUMENT_ID);

        smartRecommendationEngine.emitExecutionRecommendationsDoneMissingQtyIncr(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @ParameterizedTest
    @EnumSource(value = OemsOrderType.class, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
    void shouldSkipCandidateWithEmptyMinQty(OemsOrderType orderType) {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestMissingMinQty(orderType));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            ETHADA_BITMEX_MISSING_MINQTY_INSTRUMENT_ID);

        smartRecommendationEngine.emitExecutionRecommendationsDoneMissingMinQty(bestExecutionRequest.getRecommendationSubscriptionId());
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }
}
