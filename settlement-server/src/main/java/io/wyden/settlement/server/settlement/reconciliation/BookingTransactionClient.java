package io.wyden.settlement.server.settlement.reconciliation;

import io.wyden.published.booking.TransactionSnapshot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class BookingTransactionClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(BookingTransactionClient.class);

    private final RestTemplate restTemplate;
    private final String bookingServiceUrl;

    public BookingTransactionClient(RestTemplate restTemplate,
                                    @Value("${booking.engine.host}") String bookingServiceUrl) {
        this.restTemplate = restTemplate;
        this.bookingServiceUrl = bookingServiceUrl;
    }

    public SequenceNumberRange getSequenceNumberRange() {
        SequenceNumberInfo info = getSequenceNumberInfo();
        return new SequenceNumberRange(info.getMin(), info.getMax(), info.getCount());
    }

    public List<TransactionWithSnapshot> getTransactionsWithSnapshotsBySequenceNumbers(List<Long> sequenceNumbers) {
        return getTransactionsWithSnapshotsBatch(sequenceNumbers, 1000);
    }

    private SequenceNumberInfo getSequenceNumberInfo() {
        try {
            String url = bookingServiceUrl + "/api/v1/reconciliation/transactions/sequence-numbers";
            ResponseEntity<SequenceNumberInfo> response = restTemplate.getForEntity(url, SequenceNumberInfo.class);
            return response.getBody() != null ? response.getBody() : new SequenceNumberInfo();
        } catch (RestClientException e) {
            LOGGER.error("Error getting sequence number info from booking service", e);
            throw new RuntimeException("Booking service unavailable");
        }
    }

    private List<TransactionWithSnapshot> getTransactionsWithSnapshotsBatch(List<Long> sequenceNumbers, int batchSize) {
        List<TransactionWithSnapshot> allTransactions = new ArrayList<>();

        for (int i = 0; i < sequenceNumbers.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, sequenceNumbers.size());
            List<Long> batch = sequenceNumbers.subList(i, endIndex);

            try {
                String url = bookingServiceUrl + "/api/v1/reconciliation/transactions/by-sequence";
                String params = "?" + batch.stream()
                    .map(seq -> "sequenceNumbers=" + seq)
                    .reduce((a, b) -> a + "&" + b)
                    .orElse("") + "&maxResults=" + batchSize;

                ResponseEntity<TransactionWithSnapshot[]> response = restTemplate.getForEntity(url + params, TransactionWithSnapshot[].class);
                if (response.getBody() != null) {
                    allTransactions.addAll(List.of(response.getBody()));
                }
            } catch (RestClientException e) {
                LOGGER.error("Error getting transactions with snapshots by sequence numbers batch from booking service", e);
            }
        }

        return allTransactions;
    }

    public List<Long> getSequenceNumbersInRange(Long minSequenceNumber, Long maxSequenceNumber) {
        List<Long> allSequenceNumbers = new ArrayList<>();
        int page = 0;
        int pageSize = 1000;
        boolean hasMore = true;

        while (hasMore) {
            try {
                String url = String.format("%s/api/v1/reconciliation/transactions/sequence-numbers?min=%d&max=%d&page=%d&size=%d",
                    bookingServiceUrl, minSequenceNumber, maxSequenceNumber, page, pageSize);
                ResponseEntity<SequenceNumberInfo> response = restTemplate.getForEntity(url, SequenceNumberInfo.class);

                if (response.getBody() != null && response.getBody().getSequenceNumbers() != null) {
                    allSequenceNumbers.addAll(response.getBody().getSequenceNumbers());
                    hasMore = Boolean.TRUE.equals(response.getBody().getHasMore());
                    page++;
                } else {
                    hasMore = false;
                }
            } catch (RestClientException e) {
                LOGGER.error("Error getting sequence numbers in range from booking service, page {}", page, e);
                hasMore = false;
            }
        }

        return allSequenceNumbers;
    }

    public List<UuidSequenceNumberPair> getSequenceNumbersByUuids(List<UUID> uuids) {
        return getSequenceNumbersByUuidsBatch(uuids, 1000);
    }

    private List<UuidSequenceNumberPair> getSequenceNumbersByUuidsBatch(List<UUID> uuids, int batchSize) {
        List<UuidSequenceNumberPair> allPairs = new ArrayList<>();

        for (int i = 0; i < uuids.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uuids.size());
            List<UUID> batch = uuids.subList(i, endIndex);

            try {
                String url = bookingServiceUrl + "/api/v1/reconciliation/transactions/by-uuid";
                String params = "?" + batch.stream()
                    .map(uuid -> "uuids=" + uuid.toString())
                    .collect(Collectors.joining("&")) + "&maxResults=" + batchSize;

                ResponseEntity<UuidSequenceNumberPair[]> response = restTemplate.getForEntity(url + params, UuidSequenceNumberPair[].class);

                if (response.getBody() != null) {
                    allPairs.addAll(Arrays.asList(response.getBody()));
                }
            } catch (RestClientException e) {
                LOGGER.error("Error getting sequence numbers by UUIDs from booking service, batch size: {}", batch.size(), e);
            }
        }

        return allPairs;
    }

    public long countTransactionsWithSequenceNumber() {
        SequenceNumberRange range = getSequenceNumberRange();
        return range.count() != null ? range.count() : 0;
    }

    public static class SequenceNumberInfo {
        private Long min;
        private Long max;
        private Long count;
        private List<Long> sequenceNumbers;

        public SequenceNumberInfo() {
        }

        public SequenceNumberInfo(Long min, Long max, Long count, List<Long> sequenceNumbers) {
            this.min = min;
            this.max = max;
            this.count = count;
            this.sequenceNumbers = sequenceNumbers;
        }

        // Getters and setters
        public Long getMin() {
            return min;
        }

        public void setMin(Long min) {
            this.min = min;
        }

        public Long getMax() {
            return max;
        }

        public void setMax(Long max) {
            this.max = max;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

        public List<Long> getSequenceNumbers() {
            return sequenceNumbers;
        }

        public void setSequenceNumbers(List<Long> sequenceNumbers) {
            this.sequenceNumbers = sequenceNumbers;
        }

        public Integer getPage() {
            return page;
        }

        public void setPage(Integer page) {
            this.page = page;
        }

        public Integer getSize() {
            return size;
        }

        public void setSize(Integer size) {
            this.size = size;
        }

        public Boolean getHasMore() {
            return hasMore;
        }

        public void setHasMore(Boolean hasMore) {
            this.hasMore = hasMore;
        }
    }

    public record SequenceNumberRange(Long min, Long max, Long count) {

        @Override
        public String toString() {
            return String.format("[%d, %d] (count: %d)", min, max, count);
        }
    }

    public record TransactionWithSnapshot(Long sequenceNumber, TransactionSnapshot transactionSnapshot) {
    }

    public static class UuidSequenceNumberPair {
        private UUID uuid;
        private Long sequenceNumber;

        public UuidSequenceNumberPair() {}

        public UuidSequenceNumberPair(UUID uuid, Long sequenceNumber) {
            this.uuid = uuid;
            this.sequenceNumber = sequenceNumber;
        }

        public UUID uuid() {
            return uuid;
        }

        public Long sequenceNumber() {
            return sequenceNumber;
        }

        public UUID getUuid() {
            return uuid;
        }

        public void setUuid(UUID uuid) {
            this.uuid = uuid;
        }

        public Long getSequenceNumber() {
            return sequenceNumber;
        }

        public void setSequenceNumber(Long sequenceNumber) {
            this.sequenceNumber = sequenceNumber;
        }
    }
}
