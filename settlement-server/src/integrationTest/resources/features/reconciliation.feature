Feature: Settlement Reconciliation
  As a settlement system
  I want to reconcile transactions between booking and settlement services
  So that I can ensure data consistency and handle discrepancies

  Background:
    Given Venue Accounts
      | accountId | venueName |
      | simulator | Simulator |
    And Assume current date is '*************'

  Scenario: Successful reconciliation with matching sequence numbers
    Given Assume following transaction
      | uuid         | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | quantity     | 1                                    |
      | price        | 70000                                |
      | baseCurrency | BTC                                  |
      | currency     | USD                                  |
      | venueAccount | simulator                            |
    And Transaction has sequence number 1001 in booking service
    And Transaction exists in settlement service with sequence number 1001
    When Reconciliation is performed
    Then Reconciliation result should be successful
    And No discrepancies should be found
    And Reconciliation status should be "COMPLETED"

  Scenario: Reconciliation finds missing sequence numbers in settlement
    Given Booking service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1002          | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1003          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    And Settlement service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1003          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    When Reconciliation is performed
    Then Reconciliation result should identify missing sequence numbers
      | sequenceNumber |
      | 1002          |
    And Missing transactions should be pulled from booking service
    And Reconciliation status should be "COMPLETED_WITH_DISCREPANCIES"

  Scenario: Reconciliation finds extra sequence numbers in settlement
    Given Booking service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1003          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    And Settlement service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1002          | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1003          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    When Reconciliation is performed
    Then Reconciliation result should identify extra sequence numbers
      | sequenceNumber |
      | 1002          |
    And Extra transactions should be flagged for investigation
    And Reconciliation status should be "COMPLETED_WITH_DISCREPANCIES"

  Scenario: Backfill sequence numbers for existing transactions
    Given Settlement service has transactions without sequence numbers
      | uuid                                 |
      | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    And Booking service has sequence numbers for these transactions
      | uuid                                 | sequenceNumber |
      | 50f90caa-660e-41f4-adfa-cf2031a9109f | 1001          |
      | 60f90caa-660e-41f4-adfa-cf2031a9109f | 1002          |
      | 70f90caa-660e-41f4-adfa-cf2031a9109f | 1003          |
    When Backfill sequence numbers is performed
    Then Sequence numbers should be updated in settlement service
      | uuid                                 | sequenceNumber |
      | 50f90caa-660e-41f4-adfa-cf2031a9109f | 1001          |
      | 60f90caa-660e-41f4-adfa-cf2031a9109f | 1002          |
      | 70f90caa-660e-41f4-adfa-cf2031a9109f | 1003          |
    And Backfill status should be "COMPLETED"

  Scenario: Reconciliation with large batch processing
    Given Booking service has 1000 transactions with sequence numbers from 1 to 1000
    And Settlement service has 995 transactions with some missing sequence numbers
    When Reconciliation is performed with batch size 100
    Then Reconciliation should process all batches successfully
    And Missing transactions should be identified and pulled
    And Reconciliation should complete within reasonable time
    And Reconciliation status should be "COMPLETED_WITH_DISCREPANCIES"

  Scenario: Reconciliation handles booking service unavailable
    Given Settlement service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1002          | 60f90caa-660e-41f4-adfa-cf2031a9109f |
    And Booking service is unavailable
    When Reconciliation is performed
    Then Reconciliation should fail gracefully
    And Reconciliation status should be "FAILED"
    And Error message should indicate booking service unavailability

  Scenario: Reconciliation with sequence number gaps
    Given Booking service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1005          | 60f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1010          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    And Settlement service has transactions with sequence numbers
      | sequenceNumber | uuid                                 |
      | 1001          | 50f90caa-660e-41f4-adfa-cf2031a9109f |
      | 1010          | 70f90caa-660e-41f4-adfa-cf2031a9109f |
    When Reconciliation is performed
    Then Reconciliation should identify sequence number gaps
      | sequenceNumber |
      | 1005          |
    And Missing transactions should be pulled from booking service
    And Reconciliation status should be "COMPLETED_WITH_DISCREPANCIES"
