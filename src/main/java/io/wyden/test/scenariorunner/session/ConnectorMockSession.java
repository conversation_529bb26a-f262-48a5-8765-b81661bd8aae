package io.wyden.test.scenariorunner.session;

import ch.algotrader.api.connector.marketdata.domain.BidAskQuoteDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.marketdata.domain.OrderBookDTO;
import ch.algotrader.api.connector.marketdata.domain.OrderBookLevelDTO;

import io.qameta.allure.Step;
import io.wyden.published.venue.VenueReconciliationRequest;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueResponse;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.refdata.PortfolioFactory;
import io.wyden.test.scenariorunner.integration.tool.connectormock.ConnectorMockActor;
import io.wyden.test.scenariorunner.util.MessageQueue;
import org.assertj.core.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION;
import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION_M;
import static io.wyden.test.scenariorunner.data.ErrorMsg.INSUFFICIENT_FUNDS_REJECT_MSG;

/**
 * This class presents session of specific mock venue account.<br>
 * It uses {@link ConnectorMockActor} to control specific account registered in connector-wrapper-mock service.
 */
//TODO: refactor ConnectorMockSession to full capabilites of ConnectorMockActor
//ConnectorMockActor should be simple REST client but ConnectorMockSession - session for specific account
public class ConnectorMockSession {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectorMockSession.class);

    private static final int DEFAULT_BID_ASK_SIZE = 100_000;
    private static final double DEFAULT_BID_ASK_PRICE_GAP = 0.01;

    private final ConnectorMockActor connectorMockActor;
    private String account;

    private VenueRequest order;
    private String extId;
    private final MessageQueue<VenueRequest> orderQueue = new MessageQueue<>();
    private final MessageQueue<VenueRequest> cancelQueue = new MessageQueue<>();
    private final MessageQueue<VenueReconciliationRequest> reconciliationQueue = new MessageQueue<>();

    public ConnectorMockSession(ConnectorMockActor connectorMockActor, String account) {
        this.connectorMockActor = connectorMockActor;
        this.setAccount(account);
    }

    public String getAccount() {
        return this.account;
    }

    @Step("[{0}] Reconnect to MOCK venue order, cancel, reconciliation queues")
    public void setAccount(String account) {
        this.account = account;
        this.orderQueue.disconnect();
        this.cancelQueue.disconnect();
        this.reconciliationQueue.disconnect();
        this.orderQueue.connect(connectorMockActor.management().getVenueOrderFlux(account));
        this.cancelQueue.connect(connectorMockActor.management().getVenueCancelFlux(account));
        this.reconciliationQueue.connect(connectorMockActor.management().getVenueReconciliationRequestFlux(account));
    }

    @Step("Disconnect from MOCK venue order, cancel, reconciliation queues")
    public void dispose() {
        this.orderQueue.disconnect();
        this.cancelQueue.disconnect();
        this.reconciliationQueue.disconnect();
    }

    public void acceptNewOrder() {
        acceptNewOrder(Timeouts.WAIT_FOR_CONDITION);
    }

    @Step("Accept new order by connector with timeout {0}")
    public void acceptNewOrder(Duration duration) {
        takeOrderFromQueue(duration);
        String intId = order.getIntId();
        extId = UUID.randomUUID().toString();
        connectorMockActor.commands().acceptOrder(account, intId, extId);
    }

    @Step("Verify if no orders received by connector during {0}")
    public void verifyNoOrdersReceived(Duration duration) {
        LOGGER.info("Verify no orders received by connector during {}", duration);
        Assertions.assertThatExceptionOfType(IllegalStateException.class)
            .as("Order received by connector but was not expected")
            .isThrownBy(() -> takeOrderFromQueue(duration));
    }

    public void acceptNewOrderNoExtId() {
        takeOrderFromQueue();

        String intId = order.getIntId();
        extId = UUID.randomUUID().toString();
        connectorMockActor.commands().acceptOrder(account, intId);
    }

    private void takeOrderFromQueue() {
        takeOrderFromQueue(Timeouts.WAIT_FOR_CONDITION);
    }

    @Step("Waiting for order to arrive to connector for {0}")
    private void takeOrderFromQueue(Duration duration) {
        LOGGER.info("[{}] Waiting for order to arrive for next: {} s", account, duration.toSeconds());
        order = orderQueue.next(duration);

        if (order == null) {
            throw new IllegalStateException(("[%s] Expected an Order request to be present pending Order queue, " +
                "but was null within %d seconds - Order request likely didn't arrive to the ConnectorActor").formatted(account, duration.getSeconds()));
        }
    }

    @Step("Reject new order with reason {0}")
    public void rejectNewOrder(String reason) {
        takeOrderFromQueue();
        String intId = order.getIntId();
        extId = UUID.randomUUID().toString();
        connectorMockActor.commands().rejectOrder(account, intId, reason);
    }

    public void rejectNewOrder() {
        rejectNewOrder(INSUFFICIENT_FUNDS_REJECT_MSG);
    }

    public void receiveNewOrder() {
        takeOrderFromQueue();
    }

    public void emitCorrelationMessage() {
        connectorMockActor.commands().emitCorrelationMessage(account, order.getIntId(), extId);
        connectorMockActor.commands().reportNew(account, extId);
    }

    public void emitAcceptedMessage() {
        String intId = order.getIntId();
        connectorMockActor.commands().acceptOrder(account, intId, extId);
    }

    public void emitExpired(double cumQty) {
        String intId = order.getIntId();
        connectorMockActor.commands().reportExpired(account, intId, extId, cumQty);
    }

    @Step("Accept cancel of cumQty {0}")
    public void acceptCancel(double cumQty) {
        VenueRequest venueCancel = cancelQueue.next(WAIT_FOR_CONDITION);
        if (venueCancel != null) {
            connectorMockActor.commands().reportCancelled(account, venueCancel.getIntId(), venueCancel.getExtId(), cumQty);
            connectorMockActor.commands().acceptCancel(account, venueCancel.getIntId(), venueCancel.getExtId());
        } else {
            LOGGER.warn("[{}] Requested 'acceptCancel' but pending VenueCancel queue is empty!", account);
        }
    }

    @Step("Reject cancel with reason {0}")
    public void rejectCancel(String reason) {
        VenueRequest venueCancel = cancelQueue.next(WAIT_FOR_CONDITION);
        if (venueCancel != null) {
            connectorMockActor.commands().rejectCancel(account, venueCancel.getIntId(), venueCancel.getExtId(), reason);
        } else {
            LOGGER.warn("[{}] Requested 'rejectCancel' on but pending VenueCancel queue is empty!", account);
        }
    }

    public void reportCancelled(String intId, String extId, double cumQty) {
        connectorMockActor.commands().reportCancelled(account, intId, extId, cumQty);
    }

    public VenueRequest getOrder() {
        return order;
    }

    public double getOrderQuantity() {
        return Double.parseDouble(order.getQuantity());
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }

    public String getExtId() {
        return this.extId;
    }

    public void fillPart(String executionId, double lastQty, double lastPrice) {
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, lastPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillPart(String executionId, double lastQty, double lastPrice, double avgPrice) {
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, avgPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillPart(String executionId, double lastQty, double lastPrice, BigDecimal avgPrice) {
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, avgPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillPart(double lastQty, double lastPrice) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, lastPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillPart(double lastQty, double lastPrice, double avgPrice) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, avgPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillPart(String executionId, double lastQty, double lastPrice, double avgPrice, double fee, String feeCurrency) {
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
    }

    public void fillPart(double lastQty, double lastPrice, double avgPrice, double fee, String feeCurrency) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportPartiallyFilled(account, extId, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
    }

    public void fillFull(double cumQty, double price) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, cumQty, executionId, price, price, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillFull(String executionId, double cumQty, double price) {
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, cumQty, executionId, price, price, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillFull(String executionId, double cumQty, double price, double fee, String feeCurrency) {
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, cumQty, executionId, price, price, fee, feeCurrency);
    }

    public void fillFull(String executionId, double cumQty, double lastQty, double lastPrice, double avgPrice) {
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, lastQty, executionId, lastPrice, avgPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillFull(double cumQty, double lastQty, double lastPrice) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, lastQty, executionId, lastPrice, lastPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillFull(double cumQty, double lastQty, double lastPrice, double avgPrice) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, lastQty, executionId, lastPrice, avgPrice, 0, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public void fillFull(String executionId, double cumQty, double lastQty, double lastPrice, double avgPrice, double fee, String feeCurrency) {
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
    }

    public void fillFull(double cumQty, double lastQty, double lastPrice, double avgPrice, double fee, String feeCurrency) {
        String executionId = randomExecutionId();
        connectorMockActor.commands().reportFilledFully(account, extId, cumQty, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
    }

    /**
     * Emit separate fee ER with last generated extId
     */
    public void emitFee(double fee, String feeCurrency) {
        connectorMockActor.commands().reportFee(account, extId, BigDecimal.valueOf(fee), feeCurrency);
    }

    public void configureMarketData(List<MarketDataEventDTO> marketData) {
        connectorMockActor.management().configureMarketData(account, marketData);
    }

    public void sendBidAskQuote(String ticker, BigDecimal expectedMarketPrice) {
        sendBidAskQuote(ticker, expectedMarketPrice, DEFAULT_BID_ASK_SIZE, DEFAULT_BID_ASK_SIZE);
    }

    public void sendBidAskQuote(String ticker, BigDecimal expectedMarketPrice, double bidSize, double askSize) {
        double gap = DEFAULT_BID_ASK_PRICE_GAP;
        double bid = expectedMarketPrice.subtract(BigDecimal.valueOf(gap)).doubleValue();
        double ask = expectedMarketPrice.add(BigDecimal.valueOf(gap)).doubleValue();
        BidAskQuoteDTO quote = BidAskQuoteDTO.builder()
            .setTickerId(ticker)
            .setDateTime(ZonedDateTime.now())
            .setBidPrice(bid)
            .setBidSize(bidSize)
            .setAskPrice(ask)
            .setAskSize(askSize)
            .create();
        connectorMockActor.management().configureMarketData(account, List.of(quote));
    }

    public void sendBidAskQuote(String ticker, double bid, double ask) {
        BidAskQuoteDTO quote = BidAskQuoteDTO.builder()
            .setTickerId(ticker)
            .setDateTime(ZonedDateTime.now())
            .setBidPrice(bid) // TODO: Maybe use BigDecimal instead of double to get rid of rounding errors?
            .setBidSize(DEFAULT_BID_ASK_SIZE)
            .setAskPrice(ask)
            .setAskSize(DEFAULT_BID_ASK_SIZE)
            .create();
        connectorMockActor.management().configureMarketData(account, List.of(quote));
    }

    public void sendBidAskQuote(String ticker, double bidPrice, double bidSize, double askPrice, double askSize) {
        BidAskQuoteDTO quote = BidAskQuoteDTO.builder()
            .setTickerId(ticker)
            .setDateTime(ZonedDateTime.now())
            .setBidPrice(bidPrice)
            .setBidSize(bidSize)
            .setAskPrice(askPrice)
            .setAskSize(askSize)
            .create();
        connectorMockActor.management().configureMarketData(account, List.of(quote));
    }

    public void sendOrderBook(String ticker, double marketPrice) {
        sendOrderBook(ticker, marketPrice, DEFAULT_BID_ASK_SIZE, marketPrice, DEFAULT_BID_ASK_SIZE);
    }

    public OrderBookDTO sendOrderBook(String ticker, double bidPrice, double bidSize, double askPrice, double askSize) {
        BigDecimal askPriceBd = BigDecimal.valueOf(askPrice);
        BigDecimal bidPriceBd = BigDecimal.valueOf(bidPrice);
        OrderBookDTO orderBook = OrderBookDTO.builder()
            .setTickerId(ticker)
            .setDateTime(ZonedDateTime.now())
            .setAsks(Map.of(
                askPriceBd, new OrderBookLevelDTO(askPriceBd, BigDecimal.valueOf(askSize), 1)
            ))
            .setBids(Map.of(
                bidPriceBd, new OrderBookLevelDTO(bidPriceBd, BigDecimal.valueOf(bidSize), 1)
            ))
            .create();
        connectorMockActor.management().configureMarketData(account, List.of(orderBook));
        return orderBook;
    }

    public OrderBookDTO sendOrderBook(String ticker, Map<BigDecimal, OrderBookLevelDTO> bids, Map<BigDecimal, OrderBookLevelDTO> asks) {
        OrderBookDTO orderBook = OrderBookDTO.builder()
            .setTickerId(ticker)
            .setDateTime(ZonedDateTime.now())
            .setBids(bids)
            .setAsks(asks)
            .create();
        connectorMockActor.management().configureMarketData(account, List.of(orderBook));
        return orderBook;
    }

    public void sendOrderBook(OrderBookDTO orderBookDTO) {
        connectorMockActor.management().configureMarketData(account, orderBookDTO);
    }

    public void relayMarketData(MarketDataEventDTO mdEvent, int rateInMillis) {
        connectorMockActor.commands().relayMarketData(mdEvent, account, rateInMillis, null);
    }

    public void relayMarketData(MarketDataEventDTO mdEvent, int rateInMillis, Double randomFactor) {
        connectorMockActor.commands().relayMarketData(mdEvent, account, rateInMillis, randomFactor);
    }

    public void stopRelayMarketData() {
        connectorMockActor.commands().stopRelayMarketData(account);
    }

    @Step
    public VenueReconciliationRequest takeReconciliationRequest() {
        Duration duration = WAIT_FOR_CONDITION_M;
        LOGGER.info("[{}] Waiting for reconciliation request to arrive for next: {} s", account, duration);
        VenueReconciliationRequest reconciliationRequest = reconciliationQueue.next(duration);

        if (reconciliationRequest == null) {
            throw new IllegalStateException(("[%s] Expected Reconciliation request to be present in pending requests queue, " +
                "but was null within %d seconds - Reconciliation request likely didn't arrive to the ConnectorActor").formatted(account, duration.getSeconds()));
        }
        return reconciliationRequest;
    }

    public void configureReconciliationResponses(Duration delay, List<VenueResponse> responses) {
        connectorMockActor.commands().configureReconciliationResponses(account, delay, responses);
    }

    public VenueResponse configureReconciliationFilledFully(String executionId, BigDecimal cumQty, BigDecimal price) {
        return connectorMockActor.commands().configureSingleReconciliationResponseFilledFully(account, extId, executionId, cumQty, price, BigDecimal.ZERO, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public VenueResponse configureReconciliationFilledPart(String executionId, BigDecimal leavesQty, BigDecimal cumQty, BigDecimal avgPrice) {
        return connectorMockActor.commands().configureSingleReconciliationResponseFilledPart(account, extId, executionId, leavesQty, cumQty, avgPrice, BigDecimal.ZERO, PortfolioFactory.DEFAULT_BOOKING_CURRENCY);
    }

    public VenueResponse configureReconciliationRejected(String reason) {
        return connectorMockActor.commands().configureSingleReconciliationRejected(account, extId, reason);
    }

    public VenueResponse configureReconciliationCancelled(BigDecimal cumQty) {
        return connectorMockActor.commands().configureSingleReconciliationCancelled(account, extId, cumQty);
    }

    public void enableReconciliation() {
        connectorMockActor.management().enableReconciliation(account);
    }

    public void reportState(ConnectorMockActor.DiagnosticEvent diagnosticEvent) {
        connectorMockActor.management().reportState(account, diagnosticEvent);
    }

    private static String randomExecutionId() {
        return UUID.randomUUID().toString();
    }

    @Override
    public String toString() {
        return "ConnectorMockSession{" +
            "account='" + account + '\'' +
            '}';
    }

}
