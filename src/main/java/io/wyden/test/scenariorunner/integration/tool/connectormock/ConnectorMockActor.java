package io.wyden.test.scenariorunner.integration.tool.connectormock;

import ch.algotrader.api.connector.marketdata.domain.BidAskQuoteDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.marketdata.domain.OrderBookDTO;

import io.qameta.allure.Step;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.diagnostic.Capability;
import io.wyden.published.diagnostic.ConnectionState;
import io.wyden.published.diagnostic.HealthStatus;
import io.wyden.published.venue.VenueExecType;
import io.wyden.published.venue.VenueOrderStatus;
import io.wyden.published.venue.VenueReconciliationRequest;
import io.wyden.published.venue.VenueRequest;
import io.wyden.published.venue.VenueResponse;
import io.wyden.published.venue.VenueResponseType;
import io.wyden.test.scenariorunner.config.Configuration;
import io.wyden.test.scenariorunner.config.HostSettings;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountFactory;
import io.wyden.test.scenariorunner.integration.WebClientConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.logging.Level;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * This class is an actor to control connector-wrapper-mock service.<br>
 * Connector-wrapper-mock support several registered accounts.<br>
 * Each of those accounts can be configured to behave differently via provided endpoints.<br>
 * Almost all endpoints consume accountName as a parameter to manage specific registered account.
 */
public class ConnectorMockActor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConnectorMockActor.class);
    private static final String ISSUER_NAME = "ISSUER_MOCK";

    private final Management management = new Management();
    private final Assertions assertions = new Assertions();
    private final Commands commands = new Commands();

    private final ConnectorMockCommands connectorMockCommands;
    private final ConnectorMockQueries connectorMockQueries;

    public ConnectorMockActor() {
        HostSettings settings = Configuration.getHostSettings();
        WebClient webclient = WebClient.create(settings.connectorMockActorHost());
        connectorMockCommands = new ConnectorMockCommands(webclient);
        connectorMockQueries = new ConnectorMockQueries(webclient);
    }

    public Management management() {
        return management;
    }

    public Assertions assertions() {
        return assertions;
    }

    public Commands commands() {
        return commands;
    }

    public class Management {

        @Step
        public String newVenueAccount() {
            String accountName = VenueAccountFactory.randomVenueAccountName();
            activateVenueAccount(accountName);
            LOGGER.info("A new Connector venue account created with name: {}", accountName);
            return accountName;
        }

        @Step("[{0}] Activate venue account")
        public void activateVenueAccount(String accountName) {
            LOGGER.info("Connector actor {} - activating...", accountName);
            connectorMockCommands.activate(accountName);
            LOGGER.info("Connector actor {} - activated", accountName);
        }

        @Step("[{0}] Report state {1}")
        public void reportState(String accountName, DiagnosticEvent event) {
            connectorMockCommands.reportState(accountName, event);
            LOGGER.info("Connector actor {} - reported state {}", accountName, event);
        }

        @Step
        public String makeConnectorAbsent() {
            String accountName = VenueAccountFactory.randomVenueAccountName();
            return makeConnectorAbsent(accountName);
        }

        @Step("[{0}] Make connector absent")
        public String makeConnectorAbsent(String accountName) {
            LOGGER.info("Connector actor {} - locking venue account from picking messages and responding...", accountName);
            connectorMockCommands.keepShutOff(accountName);
            LOGGER.info("Connector actor {} - locked", accountName);
            return accountName;
        }

        @Step("[{0}] Cleanup")
        public void cleanup(@Nullable String venueAccountName) {
            if (venueAccountName == null) {
                return;
            }
            LOGGER.info("Connector actor {} - cleaning...", venueAccountName);
            connectorMockCommands.cleanup(venueAccountName);
            connectorMockCommands.clearMarketData(venueAccountName);
            LOGGER.info("Connector actor {} - cleaned", venueAccountName);
        }

        @Step("[{0}] Get venue order flux")
        public Flux<VenueRequest> getVenueOrderFlux(String accountName) {
            return connectorMockQueries.getVenueOrderFlux(accountName)
                    .log("[%s] Venue Order Flux".formatted(accountName), Level.INFO, WebClientConstants.DEFAULT_LOGGED_SIGNAL_TYPES);
        }

        @Step("[{0}] Get venue cancel flux")
        public Flux<VenueRequest> getVenueCancelFlux(String accountName) {
            return connectorMockQueries.getVenueCancelFlux(accountName)
                    .log("[%s] Venue Cancel Flux".formatted(accountName), Level.INFO, WebClientConstants.DEFAULT_LOGGED_SIGNAL_TYPES);
        }

        @Step("[{0}] Get venue reconciliation request flux")
        public Flux<VenueReconciliationRequest> getVenueReconciliationRequestFlux(String accountName) {
            return connectorMockQueries.getReconciliationRequests(accountName)
                .log("[%s] Venue Reconciliation Requests Flux".formatted(accountName), Level.INFO, WebClientConstants.DEFAULT_LOGGED_SIGNAL_TYPES);
        }

        @Step("[{0}] Configure market data {1}")
        public void configureMarketData(String venueAccount, List<MarketDataEventDTO> marketData) {
            for (MarketDataEventDTO md : marketData) {
                LOGGER.info("Configuring account {} market data {}", venueAccount, md);
                connectorMockCommands.configureMarketData(venueAccount, md);
            }
        }

        @Step("[{0}] Configure market data {1}")
        public void configureMarketData(String venueAccount, MarketDataEventDTO marketData) {
            LOGGER.info("Configuring account {} market data {}", venueAccount, marketData);
            connectorMockCommands.configureMarketData(venueAccount, marketData);
        }

        @Step("[{0}] Clear market data")
        public void clearMarketData(String venueAccount) {
            LOGGER.info("Requested Cleaning {} market data cache...", venueAccount);
            connectorMockCommands.clearMarketData(venueAccount);
        }

        @Step("[{0}] Enable reconciliation")
        public void enableReconciliation(String venueAccount) {
            LOGGER.info("Requested {} enabling reconciliation", venueAccount);
            connectorMockCommands.toggleReconciliation(venueAccount, true);
        }

        @Step("[{0}] Disable reconciliation")
        public void disableReconciliation(String venueAccount) {
            LOGGER.info("Requested {} disabling reconciliation", venueAccount);
            connectorMockCommands.toggleReconciliation(venueAccount, false);
        }

    }

    public class Assertions {

        @Step("Verify connector is healthy")
        public void shouldBeHealthy() {
            assertThat(connectorMockQueries.isHealthy())
                    .as("Connector actor should be healthy")
                    .isTrue();
        }

        public VenueRequest awaitConnectorOrderReceived(String accountName) {
            return connectorMockQueries.awaitNewSingleOrderReceived(accountName);
        }

        public VenueRequest awaitCancelRequestReceived(String accountName, String extId) {
            return connectorMockQueries.awaitOrderCancelReceived(accountName, extId);
        }
    }


    public class Commands {

        @Step("[{1}] Relay MD events {0} with rate {2} ms and randomFactor {3}")
        public void relayMarketData(MarketDataEventDTO mdEvent, String venueAccount, int rateInMillis, Double randomFactor) {
            LOGGER.info("Requested MarketDataEventDTO relay to venueAccount: '{}', rateInMillis: {}", venueAccount, rateInMillis);
            LOGGER.info("Relayed MarketDataEventDTO: {}", mdEvent.toString());
            if (mdEvent instanceof BidAskQuoteDTO event) {
                connectorMockCommands.relayBidAsks(event, venueAccount, rateInMillis, randomFactor);
            } else if (mdEvent instanceof OrderBookDTO event) {
                connectorMockCommands.relayOrderBooks(event, venueAccount, rateInMillis, randomFactor);
            } else {
                LOGGER.warn("Relaying md events {} not supported", mdEvent.getClass().getSimpleName());
            }
        }

        @Step("[{0}] Stop relay market data")
        public void stopRelayMarketData(String venueAccount) {
            LOGGER.info("Requested MarketDataEventDTO relay stop for venueAccount: '{}'", venueAccount);
            connectorMockCommands.stopRelayMarketData(venueAccount);
        }

        @Step("[{0}] Emit execution report {1}")
        public void emit(String accountName, VenueResponse executionReport) {
            LOGGER.debug("Emit execution report: executionReport={}", executionReport);
            connectorMockCommands.pushExecutionReport(accountName, executionReport);
        }

        @Step("[{0}] Reject all with reason: {1}")
        public void rejectAll(String accountName, String reason) {
            LOGGER.debug("Reject all: accountName={}, reason={}", accountName, reason);
            connectorMockCommands.rejectAll(accountName, reason);
        }

        @Step("[{0}] Accept order intId={1}, extId={2}")
        public void acceptOrder(String accountName, String intId, String extId) {
            LOGGER.info("Ack order ACCEPTED: accountName = ({}), intId = ({}), extId = ({})",
                    accountName, intId, extId);
            connectorMockCommands.acceptVenueOrder(accountName, intId, extId);
            reportNew(accountName, extId);
        }

        @Step("[{0}] Accept order intId={1}")
        public void acceptOrder(String accountName, String intId) {
            LOGGER.info("Ack order ACCEPTED: accountName = ({}), intId = ({})",
                    accountName, intId);
            connectorMockCommands.acceptVenueOrder(accountName, intId);
        }

        @Step("[{0}] Reject order intId={1}, reason={2}")
        public void rejectOrder(String accountName, String intId, String reason) {
            LOGGER.info("Ack order REJECTED: accountName = ({}), intId = ({}), reason = ({})",
                    accountName, intId, reason);
            connectorMockCommands.rejectVenueOrder(accountName, intId, reason);
        }

        @Step("[{0}] Report filled fully extId={1}")
        public void reportNew(String accountName, String extId) {
            LOGGER.info("Push execution report NEW: accountName = ({}), extId = ({})", accountName, extId);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse.Builder executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_NEW)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setLeavesQty("0")
                .setCumQty("0")
                .setLastQty("0")
                .setLastPrice("0")
                .setAvgPrice("0")
                .setSystemTimestamp(now)
                .setVenueTimestamp(now);

            LOGGER.debug("Report new: executionReport={}", executionReport);
            connectorMockCommands.pushExecutionReport(accountName, executionReport.build());
        }

        @Step("[{0}] Report filled fully extId={1}, cumQty={2}, lastQty={3}, executionId={4}, lastPrice={5}, avgPrice={6}, fee={7}, feeCurrency={8}")
        public void reportFilledFully(String accountName, String extId, double cumQty, double lastQty, String executionId, double lastPrice, double avgPrice, double fee, String feeCurrency) {
            LOGGER.info("Push execution report FILLED: accountName = ({}), extId = ({}), cumQty = ({}), lastQty = ({}), " +
                            "executionId = ({}), lastPrice = ({}), avgPrice = ({}), fee = ({}), feeCurrency = ({})",
                    accountName, extId, cumQty, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse.Builder executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_FILL)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setLeavesQty("0")
                .setCumQty(String.valueOf(cumQty))
                .setLastQty(String.valueOf(lastQty))
                .setVenueExecutionId(String.valueOf(executionId))
                .setLastPrice(String.valueOf(lastPrice))
                .setAvgPrice(String.valueOf(avgPrice))
                .setSystemTimestamp(now)
                .setVenueTimestamp(now);

            if (fee != 0) {
                executionReport
                    .setFee(String.valueOf(fee))
                    .setFeeCurrency(feeCurrency);
            }

            LOGGER.debug("Report filled: executionReport={}", executionReport);
            connectorMockCommands.pushExecutionReport(accountName, executionReport.build());
        }

        @Step("[{0}] Report partially filled extId={1}, lastQty={2}, executionId={3}, lastPrice={4}, avgPrice={5}, fee={6}, feeCurrency={7}")
        public void reportPartiallyFilled(String accountName, String extId, double lastQty, String executionId, double lastPrice, BigDecimal avgPrice, double fee, String feeCurrency) {
            LOGGER.info("Push execution report PARTIALLY FILLED: accountName = ({}), extId = ({}), lastQty = ({})," +
                            " executionId = ({}), lastPrice = ({}), avgPrice = ({}), fee = ({}), feeCurrency = ({})",
                    accountName, extId, lastQty, executionId, lastPrice, avgPrice, fee, feeCurrency);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setLastQty(String.valueOf(lastQty))
                .setLastPrice(String.valueOf(lastPrice))
                .setAvgPrice(String.valueOf(avgPrice))
                .setVenueExecutionId(executionId)
                .setFee(String.valueOf(fee))
                .setFeeCurrency(feeCurrency)
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            LOGGER.debug("Report partially filled: executionReport={}", executionReport);
            connectorMockCommands.pushExecutionReport(accountName, executionReport);
        }

        public void reportPartiallyFilled(String accountName, String extId, double lastQty, String executionId, double lastPrice, double avgPrice, double fee, String feeCurrency) {
            reportPartiallyFilled(accountName, extId, lastQty, executionId, lastPrice, BigDecimal.valueOf(avgPrice), fee, feeCurrency);
        }

        @Step("[{0}] Emit correlation message intId={0}, extId={1}")
        public void emitCorrelationMessage(String accountName, String intId, String extId) {
            LOGGER.info("Push execution report CORRELATED: accountName = ({}), intId = ({}), extId = ({})",
                    accountName, intId, extId);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse execReportWithCorrelation = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setExtId(extId)
                .setIntId(intId)
                .setVenueAccount(accountName)
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .build();
            LOGGER.debug("Emit correlation message: executionReport={}", execReportWithCorrelation);
            connectorMockCommands.pushExecutionReport(accountName, execReportWithCorrelation);
        }

        @Step("[{0} Accept cancel intId={1}, extId={2}]")
        public void acceptCancel(String accountName, String intId, String extId) {
            LOGGER.info("Ack cancel ACCEPTED: accountName = ({}), intId = ({}), extId = ({})",
                accountName, intId, extId);
            connectorMockCommands.acceptVenueCancel(accountName, intId, extId);
        }

        @Step("[{0}] Reject cancel intId={1}, extId={2}, reason={3}")
        public void rejectCancel(String accountName, String intId, String extId, String reason) {
            LOGGER.info("Ack cancel REJECTED: accountName = ({}), intId = ({}), extId = ({}), reason = ({})",
                    accountName, intId, extId, reason);
            connectorMockCommands.rejectVenueCancel(accountName, intId, extId, reason);
        }

        @Step("[{0}] Report cancelled extId={1}, cumQty={2}")
        public void reportCancelled(String accountName, String intId, String extId, double cumQty) {
            LOGGER.info("Push execution report CANCELLED: accountName = ({}), extId = ({}), intId = ({}), cumQty = ({})",
                accountName, extId, intId, cumQty);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse cancelledER = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setIntId(intId)
                .setExtId(extId)
                .setVenueAccount(accountName)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_CANCELED)
                .setCumQty(String.valueOf(cumQty))
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            LOGGER.debug("Report canceled: executionReport={}", cancelledER);
            connectorMockCommands.pushExecutionReport(accountName, cancelledER);
        }

        @Step("[{0}] Report expired extId={1}, cumQty={2}")
        public void reportExpired(String accountName, String intId, String extId, double cumQty) {
            LOGGER.info("Push execution report EXPIRED: accountName = ({}), extId = ({}), intId = ({}), cumQty = ({})",
                accountName, extId, intId, cumQty);
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse expiredResponse = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setVenueExecutionId(UUID.randomUUID().toString())
                .setIntId(intId)
                .setExtId(extId)
                .setVenueAccount(accountName)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_EXPIRED)
                .setCumQty(String.valueOf(cumQty))
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            LOGGER.debug("Report expired: executionReport={}", expiredResponse);
            connectorMockCommands.pushExecutionReport(accountName, expiredResponse);
        }

        @Step("[{0}] Configure reconciliation responses {2} with delay {1}")
        public void configureReconciliationResponses(String accountName, Duration delay, List<VenueResponse> responses) {
            LOGGER.info("Configure reconciliation response: accountName={}, delay={}, responses={}", accountName, delay, responses);
            connectorMockCommands.configureReconciliationResponse(accountName, delay, responses);
        }

        @Step("[{0}] Configure single reconciliation response filled fully extId={1}, executionId={2}, cumQty={3}, price={4}, fee={5}, feeCurrency={6}")
        public VenueResponse configureSingleReconciliationResponseFilledFully(String accountName, String extId, String executionId, BigDecimal cumQty, BigDecimal price, BigDecimal fee, String feeCurrency) {
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            //cumQty and avgPrice should be filled
            VenueResponse executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_RECONCILIATION_RESULT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_FILL)
                .setOrderStatus(VenueOrderStatus.FILLED)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setLeavesQty("0")
                .setCumQty(String.valueOf(cumQty))
                .setVenueExecutionId(String.valueOf(executionId))
                .setAvgPrice(String.valueOf(price))
                .setFee(String.valueOf(fee))
                .setFeeCurrency(feeCurrency)
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            configureReconciliationResponses(accountName, Duration.ZERO, List.of(executionReport));
            return executionReport;
        }

        @Step("[{0}] Configure single reconciliation response filled partially extId={1}, executionId={2}, leavesQty={3}, cumQty={4}, avgPrice={5}, fee={6}, feeCurrency={7}")
        public VenueResponse configureSingleReconciliationResponseFilledPart(String accountName, String extId, String executionId, BigDecimal leavesQty, BigDecimal cumQty, BigDecimal avgPrice, BigDecimal fee, String feeCurrency) {
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());

            VenueResponse executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_RECONCILIATION_RESULT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_PARTIAL_FILL)
                .setOrderStatus(VenueOrderStatus.PARTIALLY_FILLED)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setLeavesQty(String.valueOf(leavesQty))
                .setCumQty(String.valueOf(cumQty))
                .setVenueExecutionId(String.valueOf(executionId))
                .setAvgPrice(String.valueOf(avgPrice))
                .setFee(String.valueOf(fee))
                .setFeeCurrency(feeCurrency)
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            configureReconciliationResponses(accountName, Duration.ZERO, List.of(executionReport));
            return executionReport;
        }

        @Step("[{0}] Configure single reconciliation rejected extId={1}, reason={2}")
        public VenueResponse configureSingleReconciliationRejected(String accountName, String extId, String reason) {
            VenueResponse executionReport = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_RECONCILIATION_RESULT)
                .setExtId(extId)
                .setVenueAccount(accountName)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_REJECTED)
                .setReason(reason)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            configureReconciliationResponses(accountName, Duration.ZERO, List.of(executionReport));
            return executionReport;
        }

        @Step("[{0}] Configure single reconciliation cancelled extId={1}, cumQty={2}")
        public VenueResponse configureSingleReconciliationCancelled(String accountName, String extId, BigDecimal cumQty) {
            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());
            VenueResponse cancelledER = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_RECONCILIATION_RESULT)
                .setExtId(extId)
                .setVenueAccount(accountName)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_CANCELED)
                .setOrderStatus(VenueOrderStatus.CANCELED)
                .setCumQty(String.valueOf(cumQty))
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setIssuer(ISSUER_NAME)
                .setSettlDate(getSettlDate())
                .build();
            configureReconciliationResponses(accountName, Duration.ZERO, List.of(cancelledER));
            return cancelledER;
        }

        private String getSettlDate(){
            return Instant.now().atZone(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_LOCAL_DATE)
                .replace("-", "");
        }

        @Step("[{0}] Report fee extId={1}, fee={2}, feeCurrency={3}")
        public void reportFee(String accountName, String extId, BigDecimal fee, String feeCurrency) {

            String now = DateUtils.toIsoUtcTime(ZonedDateTime.now());

            VenueResponse.Builder execReportBuilder = VenueResponse.newBuilder()
                .setResponseType(VenueResponseType.VENUE_EXECUTION_REPORT)
                .setExecType(VenueExecType.VENUE_EXEC_TYPE_FILL)
                .setVenueAccount(accountName)
                .setExtId(extId)
                .setSystemTimestamp(now)
                .setVenueTimestamp(now)
                .setFee(String.valueOf(fee))
                .setFeeCurrency(feeCurrency)
                .setLastPrice("0")
                .setLastQty("0");

            VenueResponse execReport = execReportBuilder.build();
            LOGGER.debug("Report fee: executionReport={}", execReport);
            connectorMockCommands.pushExecutionReport(accountName, execReport);
        }
    }

    public record DiagnosticEvent(Set<Capability> capabilities, Health health) {
    }

    public record Health(String msg, HealthStatus status, Map<String, ConnectionState> connections) {
        public static Health fromStatus(HealthStatus status) {
            return new Health(status.name(), status, Map.of());
        }
    }

}
