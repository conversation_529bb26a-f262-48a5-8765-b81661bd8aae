package io.wyden.booking.snapshotter.application.reservation;

import io.wyden.booking.snapshotter.application.transaction.TransactionFromProtoMapper;
import io.wyden.booking.snapshotter.domain.common.Identifiers;
import io.wyden.booking.snapshotter.domain.reservation.FeeReservation;
import io.wyden.booking.snapshotter.domain.reservation.Reservation;
import io.wyden.booking.snapshotter.domain.reservation.payment.DepositReservation;
import io.wyden.booking.snapshotter.domain.reservation.payment.WithdrawalReservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.ClientAssetTradeReservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.ClientCashTradeReservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.StreetAssetTradeReservation;
import io.wyden.booking.snapshotter.domain.reservation.trade.StreetCashTradeReservation;
import io.wyden.booking.snapshotter.domain.reservation.transfer.AccountCashTransferReservation;
import io.wyden.booking.snapshotter.domain.reservation.transfer.PortfolioAssetTransferReservation;
import io.wyden.booking.snapshotter.domain.reservation.transfer.PortfolioCashTransferReservation;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.AccountCashTransferReservationSnapshot;
import io.wyden.published.booking.ClientAssetTradeReservationSnapshot;
import io.wyden.published.booking.ClientCashTradeReservationSnapshot;
import io.wyden.published.booking.DepositReservationSnapshot;
import io.wyden.published.booking.FeeReservationSnapshot;
import io.wyden.published.booking.PortfolioAssetTransferReservationSnapshot;
import io.wyden.published.booking.PortfolioCashTransferReservationSnapshot;
import io.wyden.published.booking.ReservationSnapshot;
import io.wyden.published.booking.StreetAssetTradeReservationSnapshot;
import io.wyden.published.booking.StreetCashTradeReservationSnapshot;
import io.wyden.published.booking.WithdrawalReservationSnapshot;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.firstNonBlank;

public final class ReservationFromProtoMapper {

    private ReservationFromProtoMapper() {
    }

    public static Reservation map(ReservationSnapshot reservationSnapshot) {
        ReservationSnapshot.ReservationCase reservationCase = reservationSnapshot.getReservationCase();
        String uuid = reservationSnapshot.getUuid().isBlank() ? Identifiers.randomIdentifier() : reservationSnapshot.getUuid();

        switch (reservationCase) {
            case CLIENT_CASH_TRADE_RESERVATION -> {
                return getClientCashTradeReservation(reservationSnapshot, uuid);
            }
            case STREET_CASH_TRADE_RESERVATION -> {
                return getStreetCashTradeReservation(reservationSnapshot, uuid);
            }
            case CLIENT_ASSET_TRADE_RESERVATION -> {
                return getClientAssetTradeReservation(reservationSnapshot, uuid);
            }
            case STREET_ASSET_TRADE_RESERVATION -> {
                return getStreetAssetTradeReservation(reservationSnapshot, uuid);
            }
            case DEPOSIT_RESERVATION -> {
                return getDepositReservation(reservationSnapshot, uuid);
            }
            case WITHDRAWAL_RESERVATION -> {
                return getWithdrawalReservation(reservationSnapshot, uuid);
            }
            case ACCOUNT_CASH_TRANSFER_RESERVATION -> {
                return getAccountCashTransferReservation(reservationSnapshot, uuid);
            }
            case PORTFOLIO_CASH_TRANSFER_RESERVATION -> {
                return getPortfolioCashTransferReservation(reservationSnapshot, uuid);
            }
            case PORTFOLIO_ASSET_TRANSFER_RESERVATION -> {
                return getPortfolioAssetTransferReservation(reservationSnapshot, uuid);
            }
            case FEE_RESERVATION -> {
                return getFeeReservation(reservationSnapshot, uuid);
            }
            default -> throw new IllegalArgumentException("Unknown reservation case: " + reservationCase);
        }
    }

    public static AccountCashTransferReservation getAccountCashTransferReservation(ReservationSnapshot reservationRequest, String uuid) {
        AccountCashTransferReservationSnapshot request = reservationRequest.getAccountCashTransferReservation();
        String feePortfolioId = firstNonBlank(request.getFeePortfolioId(), EMPTY);
        String feeAccountId = firstNonBlank(request.getFeeAccountId(), request.getFromAccountId());

        return new AccountCashTransferReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            null,
            null,
            request.getCurrency(),
            request.getFromAccountId(),
            request.getToAccountId(),
            feeAccountId,
            feePortfolioId,
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static StreetCashTradeReservation getStreetCashTradeReservation(ReservationSnapshot reservationRequest, String uuid) {
        StreetCashTradeReservationSnapshot request = reservationRequest.getStreetCashTradeReservation();

        return new StreetCashTradeReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            bd(request.getPrice()),
            bd(request.getStopPrice()),
            request.getCurrency(),
            request.getBaseCurrency(),
            request.getPortfolioId(),
            request.getAccountId(),
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static ClientCashTradeReservation getClientCashTradeReservation(ReservationSnapshot reservationRequest, String uuid) {
        ClientCashTradeReservationSnapshot request = reservationRequest.getClientCashTradeReservation();

        return new ClientCashTradeReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            bd(request.getPrice()),
            bd(request.getStopPrice()),
            request.getCurrency(),
            request.getBaseCurrency(),
            request.getPortfolioId(),
            request.getCounterPortfolioId(),
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static StreetAssetTradeReservation getStreetAssetTradeReservation(ReservationSnapshot reservationRequest, String uuid) {
        StreetAssetTradeReservationSnapshot request = reservationRequest.getStreetAssetTradeReservation();

        return new StreetAssetTradeReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            bd(request.getPrice()),
            bd(request.getStopPrice()),
            request.getCurrency(),
            request.getInstrument(),
            request.getPortfolioId(),
            request.getAccountId(),
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static ClientAssetTradeReservation getClientAssetTradeReservation(ReservationSnapshot reservationRequest, String uuid) {
        ClientAssetTradeReservationSnapshot request = reservationRequest.getClientAssetTradeReservation();

        return new ClientAssetTradeReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            TransactionFromProtoMapper.map(request.getReservationFeeList()),
            bd(request.getQuantity()),
            bd(request.getPrice()),
            bd(request.getStopPrice()),
            request.getCurrency(),
            request.getInstrument(),
            request.getPortfolioId(),
            request.getCounterPortfolioId()
        );
    }

    public static WithdrawalReservation getWithdrawalReservation(ReservationSnapshot reservationRequest, String uuid) {
        WithdrawalReservationSnapshot request = reservationRequest.getWithdrawalReservation();
        String feePortfolioId = firstNonBlank(request.getFeePortfolioId(), request.getPortfolioId());
        String feeAccountId = firstNonBlank(request.getFeeAccountId(), request.getAccountId());

        return new WithdrawalReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            request.getCurrency(),
            bd(request.getQuantity()),
            request.getPortfolioId(),
            request.getAccountId(),
            feePortfolioId,
            feeAccountId,
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static DepositReservation getDepositReservation(ReservationSnapshot reservationRequest, String uuid) {
        DepositReservationSnapshot request = reservationRequest.getDepositReservation();

        String feePortfolioId = firstNonBlank(request.getFeePortfolioId(), request.getPortfolioId());
        String feeAccountId = firstNonBlank(request.getFeeAccountId(), request.getAccountId());

        return new DepositReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            request.getCurrency(),
            bd(request.getQuantity()),
            request.getPortfolioId(),
            request.getAccountId(),
            feePortfolioId,
            feeAccountId,
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static PortfolioCashTransferReservation getPortfolioCashTransferReservation(ReservationSnapshot reservationRequest, String uuid) {
        PortfolioCashTransferReservationSnapshot request = reservationRequest.getPortfolioCashTransferReservation();

        String feePortfolioId = firstNonBlank(request.getFeePortfolioId(), request.getFromPortfolioId());

        return new PortfolioCashTransferReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            null,
            null,
            request.getCurrency(),
            request.getFromPortfolioId(),
            request.getToPortfolioId(),
            feePortfolioId,
            TransactionFromProtoMapper.map(request.getReservationFeeList())
        );
    }

    public static PortfolioAssetTransferReservation getPortfolioAssetTransferReservation(ReservationSnapshot reservationRequest, String uuid) {
        PortfolioAssetTransferReservationSnapshot request = reservationRequest.getPortfolioAssetTransferReservation();

        String feePortfolioId = firstNonBlank(request.getFeePortfolioId(), request.getFromPortfolioId());

        return new PortfolioAssetTransferReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            TransactionFromProtoMapper.map(request.getReservationFeeList()),
            bd(request.getQuantity()),
            null,
            null,
            request.getInstrument(),
            request.getFromPortfolioId(),
            request.getToPortfolioId(),
            feePortfolioId
        );
    }

    public static FeeReservation getFeeReservation(ReservationSnapshot reservationRequest, String uuid) {
        FeeReservationSnapshot request = reservationRequest.getFeeReservation();

        return new FeeReservation(
            uuid,
            request.getReservationRef(),
            DateUtils.isoUtcTimeToZonedDateTime(request.getDateTime()),
            bd(request.getQuantity()),
            request.getCurrency(),
            request.getPortfolioId(),
            request.getAccountId()
        );
    }
}
