package io.wyden.booking.snapshotter.application.transaction;

import io.wyden.booking.snapshotter.domain.transaction.ExecType;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFee;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFeeType;
import io.wyden.booking.snapshotter.domain.transaction.payment.Deposit;
import io.wyden.booking.snapshotter.domain.transaction.payment.Withdrawal;
import io.wyden.booking.snapshotter.domain.transaction.trade.ClientAssetTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.ClientCashTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.StreetAssetTrade;
import io.wyden.booking.snapshotter.domain.transaction.trade.StreetCashTrade;
import io.wyden.booking.snapshotter.domain.transaction.transfer.AccountCashTransfer;
import io.wyden.booking.snapshotter.domain.transaction.transfer.PortfolioAssetTransfer;
import io.wyden.booking.snapshotter.domain.transaction.transfer.PortfolioCashTransfer;
import io.wyden.published.booking.AccountCashTransferSnapshot;
import io.wyden.published.booking.ClientAssetTradeSnapshot;
import io.wyden.published.booking.ClientCashTradeSnapshot;
import io.wyden.published.booking.DepositSnapshot;
import io.wyden.published.booking.Fee;
import io.wyden.published.booking.FeeSnapshot;
import io.wyden.published.booking.FeeType;
import io.wyden.published.booking.PortfolioAssetTransferSnapshot;
import io.wyden.published.booking.PortfolioCashTransferSnapshot;
import io.wyden.published.booking.StreetAssetTradeSnapshot;
import io.wyden.published.booking.StreetCashTradeSnapshot;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.WithdrawalSnapshot;
import io.wyden.published.oems.OemsExecType;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.Collection;

import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static io.wyden.cloudutils.tools.DateUtils.isoUtcTimeToZonedDateTime;
import static org.apache.commons.lang3.StringUtils.firstNonBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class TransactionFromProtoMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionFromProtoMapper.class);

    private TransactionFromProtoMapper() {
    }

    public static Transaction map(TransactionSnapshot transactionSnapshot) {
        if (transactionSnapshot == null) {
            return null;
        }

        return switch (transactionSnapshot.getTransactionCase()) {
            case CLIENT_CASH_TRADE -> map(transactionSnapshot.getClientCashTrade());
            case STREET_CASH_TRADE -> map(transactionSnapshot.getStreetCashTrade());
            case CLIENT_ASSET_TRADE -> map(transactionSnapshot.getClientAssetTrade());
            case STREET_ASSET_TRADE -> map(transactionSnapshot.getStreetAssetTrade());
            case DEPOSIT -> map(transactionSnapshot.getDeposit());
            case WITHDRAWAL -> map(transactionSnapshot.getWithdrawal());
            case ACCOUNT_CASH_TRANSFER -> map(transactionSnapshot.getAccountCashTransfer());
            case PORTFOLIO_CASH_TRANSFER -> map(transactionSnapshot.getPortfolioCashTransfer());
            case PORTFOLIO_ASSET_TRANSFER -> map(transactionSnapshot.getPortfolioAssetTransfer());
            case FEE -> map(transactionSnapshot.getFee());
            default -> throw new IllegalArgumentException("Unknown transaction type: " + transactionSnapshot.getTransactionCase());
        };
    }

    public static Transaction map(ClientCashTradeSnapshot clientCashTrade) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(clientCashTrade.getDateTime());

        ClientCashTrade transaction = new ClientCashTrade(
            clientCashTrade.getUuid(),
            clientCashTrade.getReservationRef(),
            dateTime,
            clientCashTrade.getExecutionId(),
            clientCashTrade.getVenueExecutionId(),
            map(clientCashTrade.getTransactionFeeList()),
            clientCashTrade.getDescription(),
            clientCashTrade.getIsLive(),
            bd(clientCashTrade.getQuantity()),
            bd(clientCashTrade.getLeavesQuantity()),
            bd(clientCashTrade.getPrice()),
            clientCashTrade.getCurrency(),
            clientCashTrade.getIntOrderId(),
            clientCashTrade.getExtOrderId(),
            clientCashTrade.getOrderId(),
            clientCashTrade.getParentOrderId(),
            clientCashTrade.getRootOrderId(),
            clientCashTrade.getUnderlyingExecutionId(),
            clientCashTrade.getRootExecutionId(),
            clientCashTrade.getClientRootOrderId(),
            fromProto(clientCashTrade.getExecType()),
            clientCashTrade.getBaseCurrency(),
            clientCashTrade.getPortfolio(),
            clientCashTrade.getCounterPortfolio()
        );

        if (clientCashTrade.getSettled()) {
            transaction.setSettled(true);
            transaction.setSettledDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(clientCashTrade.getSettledDateTime()), ZonedDateTime.now()));
        }

        if (isNotBlank(clientCashTrade.getSettlementId())) {
            transaction.setSettlementId(Long.valueOf(clientCashTrade.getSettlementId()));
        }

        return transaction;
    }

    public static Transaction map(StreetCashTradeSnapshot streetCashTrade) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(streetCashTrade.getDateTime());

        StreetCashTrade transaction = new StreetCashTrade(
            streetCashTrade.getUuid(),
            streetCashTrade.getReservationRef(),
            dateTime,
            streetCashTrade.getExecutionId(),
            streetCashTrade.getVenueExecutionId(),
            map(streetCashTrade.getTransactionFeeList()),
            streetCashTrade.getDescription(),
            streetCashTrade.getIsLive(),
            bd(streetCashTrade.getQuantity()),
            bd(streetCashTrade.getLeavesQuantity()),
            bd(streetCashTrade.getPrice()),
            streetCashTrade.getCurrency(),
            streetCashTrade.getIntOrderId(),
            streetCashTrade.getExtOrderId(),
            streetCashTrade.getOrderId(),
            streetCashTrade.getParentOrderId(),
            streetCashTrade.getRootOrderId(),
            streetCashTrade.getUnderlyingExecutionId(),
            streetCashTrade.getRootExecutionId(),
            streetCashTrade.getClientRootOrderId(),
            fromProto(streetCashTrade.getExecType()),
            streetCashTrade.getBaseCurrency(),
            streetCashTrade.getPortfolio(),
            streetCashTrade.getVenueAccount()
        );

        if (streetCashTrade.getSettled()) {
            transaction.setSettled(true);
            transaction.setSettledDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(streetCashTrade.getSettledDateTime()), ZonedDateTime.now()));
        }

        if (isNotBlank(streetCashTrade.getSettlementId())) {
            transaction.setSettlementId(Long.valueOf(streetCashTrade.getSettlementId()));
        }

        return transaction;
    }

    public static Transaction map(ClientAssetTradeSnapshot clientAssetTrade) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(clientAssetTrade.getDateTime());

        ClientAssetTrade transaction = new ClientAssetTrade(
            clientAssetTrade.getUuid(),
            clientAssetTrade.getReservationRef(),
            dateTime,
            clientAssetTrade.getExecutionId(),
            clientAssetTrade.getVenueExecutionId(),
            map(clientAssetTrade.getTransactionFeeList()),
            clientAssetTrade.getDescription(),
            clientAssetTrade.getIsLive(),
            bd(clientAssetTrade.getQuantity()),
            bd(clientAssetTrade.getLeavesQuantity()),
            bd(clientAssetTrade.getPrice()),
            clientAssetTrade.getCurrency(),
            clientAssetTrade.getIntOrderId(),
            clientAssetTrade.getExtOrderId(),
            clientAssetTrade.getOrderId(),
            // TODO SPL booking-snapshotter - add missing order ids
            null,
            null,
            null,
            null,
            null,
            fromProto(clientAssetTrade.getExecType()),
            // TODO SPL booking-snapshotter - asset split by '/' ?
            clientAssetTrade.getInstrument(),
            clientAssetTrade.getPortfolio(),
            clientAssetTrade.getCounterPortfolio()
        );

        if (clientAssetTrade.getSettled()) {
            transaction.setSettled(true);
            transaction.setSettledDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(clientAssetTrade.getSettledDateTime()), ZonedDateTime.now()));
        }

        return transaction;
    }

    public static Transaction map(StreetAssetTradeSnapshot streetAssetTrade) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(streetAssetTrade.getDateTime());

        StreetAssetTrade transaction = new StreetAssetTrade(
            streetAssetTrade.getUuid(),
            streetAssetTrade.getReservationRef(),
            dateTime,
            streetAssetTrade.getExecutionId(),
            streetAssetTrade.getVenueExecutionId(),
            map(streetAssetTrade.getTransactionFeeList()),
            streetAssetTrade.getDescription(),
            streetAssetTrade.getIsLive(),
            bd(streetAssetTrade.getQuantity()),
            bd(streetAssetTrade.getLeavesQuantity()),
            bd(streetAssetTrade.getPrice()),
            streetAssetTrade.getCurrency(),
            streetAssetTrade.getIntOrderId(),
            streetAssetTrade.getExtOrderId(),
            streetAssetTrade.getOrderId(),
            // TODO SPL booking-snapshotter - add missing order ids
            null,
            null,
            null,
            null,
            null,
            fromProto(streetAssetTrade.getExecType()),
            // TODO SPL booking-snapshotter - asset split by '/' ?
            streetAssetTrade.getInstrument(),
            streetAssetTrade.getPortfolio(),
            streetAssetTrade.getVenueAccount()
        );

        if (streetAssetTrade.getSettled()) {
            transaction.setSettled(true);
            transaction.setSettledDateTime(ObjectUtils.firstNonNull(isoUtcTimeToZonedDateTime(streetAssetTrade.getSettledDateTime()), ZonedDateTime.now()));
        }

        return transaction;
    }

    public static Transaction map(DepositSnapshot deposit) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(deposit.getDateTime());

        String feeAccountId = firstNonBlank(deposit.getFeeAccountId(), deposit.getAccount());
        String feePortfolioId = firstNonBlank(deposit.getFeePortfolioId(), deposit.getPortfolio());

        // TODO SPL add settlement id to payments

        return new Deposit(
            deposit.getUuid(),
            deposit.getReservationRef(),
            dateTime,
            deposit.getExecutionId(),
            deposit.getVenueExecutionId(),
            map(deposit.getTransactionFeeList()),
            deposit.getDescription(),
            deposit.getIsLive(),
            deposit.getCurrency(),
            bd(deposit.getQuantity()),
            deposit.getPortfolio(),
            deposit.getAccount(),
            feeAccountId,
            feePortfolioId
        );
    }

    private static ExecType fromProto(OemsExecType execType) {
        return switch (execType) {
            case EXEC_TYPE_UNSPECIFIED, UNRECOGNIZED -> null;
            case NEW -> ExecType.NEW;
            case PARTIAL_FILL -> ExecType.PARTIAL_FILL;
            case FILL -> ExecType.FILL;
            case CANCELED -> ExecType.CANCELED;
            case PENDING_CANCEL -> ExecType.PENDING_CANCEL;
            case REJECTED -> ExecType.REJECTED;
            case PENDING_NEW -> ExecType.PENDING_NEW;
            case EXPIRED -> ExecType.EXPIRED;
            case CALCULATED -> ExecType.CALCULATED;
            case RESTATED -> ExecType.RESTATED;
        };
    }

    public static Transaction map(WithdrawalSnapshot withdrawal) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(withdrawal.getDateTime());

        String feeAccountId = firstNonBlank(withdrawal.getFeeAccountId(), withdrawal.getAccount());
        String feePortfolioId = firstNonBlank(withdrawal.getFeePortfolioId(), withdrawal.getPortfolio());

        Withdrawal transaction = new Withdrawal(
            withdrawal.getUuid(),
            withdrawal.getReservationRef(),
            dateTime,
            withdrawal.getExecutionId(),
            withdrawal.getVenueExecutionId(),
            map(withdrawal.getTransactionFeeList()),
            withdrawal.getDescription(),
            withdrawal.getIsLive(),
            withdrawal.getCurrency(),
            bd(withdrawal.getQuantity()),
            withdrawal.getPortfolio(),
            withdrawal.getAccount(),
            feeAccountId,
            feePortfolioId
        );

        // TODO SPL add settlement id to payments

        return transaction;
    }

    public static Transaction map(AccountCashTransferSnapshot accountCashTransfer) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(accountCashTransfer.getDateTime());

        String feeAccountId = firstNonBlank(accountCashTransfer.getFeeAccountId(), accountCashTransfer.getFromAccountId());
        String feePortfolioId = firstNonBlank(accountCashTransfer.getFeePortfolioId(), accountCashTransfer.getFeePortfolioId());

        AccountCashTransfer transaction = new AccountCashTransfer(
            accountCashTransfer.getUuid(),
            accountCashTransfer.getReservationRef(),
            dateTime,
            accountCashTransfer.getExecutionId(),
            accountCashTransfer.getVenueExecutionId(),
            map(accountCashTransfer.getTransactionFeeList()),
            accountCashTransfer.getDescription(),
            accountCashTransfer.getIsLive(),
            bd(accountCashTransfer.getQuantity()),
            // TODO SPL booking-snapshotter - missing transfer ids
            null,
            null,
            accountCashTransfer.getCurrency(),
            accountCashTransfer.getFromAccountId(),
            accountCashTransfer.getToAccountId(),
            feeAccountId,
            feePortfolioId
        );

        // TODO SPL add settlement id to transfers

        return transaction;
    }

    public static Transaction map(PortfolioCashTransferSnapshot portfolioCashTransfer) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(portfolioCashTransfer.getDateTime());

//        TODO remove after https://algotrader.atlassian.net/browse/AC-6119
        String feePortfolioId;
        if (!portfolioCashTransfer.getFeePortfolioId().isBlank()) {
            feePortfolioId = portfolioCashTransfer.getFeePortfolioId();
        } else {
            LOGGER.info("feePortfolioId is not set - falling back to fromPortfolio as feePortfolioId");
            feePortfolioId = portfolioCashTransfer.getFromPortfolioId();
        }

        PortfolioCashTransfer transaction = new PortfolioCashTransfer(
            portfolioCashTransfer.getUuid(),
            portfolioCashTransfer.getReservationRef(),
            dateTime,
            portfolioCashTransfer.getExecutionId(),
            portfolioCashTransfer.getVenueExecutionId(),
            map(portfolioCashTransfer.getTransactionFeeList()),
            portfolioCashTransfer.getDescription(),
            portfolioCashTransfer.getIsLive(),
            bd(portfolioCashTransfer.getQuantity()),
            // TODO SPL booking-snapshotter - missing transfer ids
            null,
            null,
            portfolioCashTransfer.getCurrency(),
            portfolioCashTransfer.getFromPortfolioId(),
            portfolioCashTransfer.getToPortfolioId(),
            feePortfolioId
        );

        // TODO SPL add settlement id to transfers

        return transaction;
    }

    public static Transaction map(PortfolioAssetTransferSnapshot portfolioAssetTransfer) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(portfolioAssetTransfer.getDateTime());

//        TODO remove after https://algotrader.atlassian.net/browse/AC-6119
        String feePortfolioId;
        if (!portfolioAssetTransfer.getFeePortfolioId().isBlank()) {
            feePortfolioId = portfolioAssetTransfer.getFeePortfolioId();
        } else {
            LOGGER.info("feePortfolioId is not set - falling back to fromPortfolio as feePortfolioId");
            feePortfolioId = portfolioAssetTransfer.getFromPortfolioId();
        }

        PortfolioAssetTransfer transaction = new PortfolioAssetTransfer(
            portfolioAssetTransfer.getUuid(),
            portfolioAssetTransfer.getReservationRef(),
            dateTime,
            portfolioAssetTransfer.getExecutionId(),
            portfolioAssetTransfer.getVenueExecutionId(),
            map(portfolioAssetTransfer.getTransactionFeeList()),
            portfolioAssetTransfer.getDescription(),
            portfolioAssetTransfer.getIsLive(),
            bd(portfolioAssetTransfer.getQuantity()),
            // TODO SPL booking-snapshotter - missing transfer ids
            null,
            null,
            // TODO SPL booking-snapshotter - asset split by '/' ?
            portfolioAssetTransfer.getInstrument(),
            portfolioAssetTransfer.getFromPortfolioId(),
            portfolioAssetTransfer.getToPortfolioId(),
            feePortfolioId
        );

        // TODO SPL add settlement id to transfers

        return transaction;
    }

    public static Transaction map(FeeSnapshot snapshot) {
        ZonedDateTime dateTime = isoUtcTimeToZonedDateTime(snapshot.getDateTime());

        io.wyden.booking.snapshotter.domain.transaction.Fee transaction = new io.wyden.booking.snapshotter.domain.transaction.Fee(
            snapshot.getUuid(),
            snapshot.getReservationRef(),
            dateTime,
            snapshot.getExecutionId(),
            snapshot.getVenueExecutionId(),
            map(snapshot.getTransactionFeeList()),
            snapshot.getDescription(),
            snapshot.getIsLive(),
            bd(snapshot.getQuantity()),
            snapshot.getCurrency(),
            snapshot.getPortfolioId(),
            snapshot.getAccountId()
        );

        if (snapshot.getSettled()) {
            transaction.setSettled(true);
            transaction.setSettledDateTime(isoUtcTimeToZonedDateTime(snapshot.getSettledDateTime()));
        }

        // TODO SPL add settlement id to fee

        return transaction;
    }

    public static Collection<TransactionFee> map(Collection<Fee> fees) {
        if (fees == null) {
            return null;
        }

        return fees.stream()
            .map(fee -> new TransactionFee(
                bd(fee.getAmount()),
                fee.getCurrency(),
                null,
                map(fee.getFeeType())))
            .toList();
    }

    public static TransactionFeeType map(FeeType feeType) {
        if (feeType == null) {
            return TransactionFeeType.FEE_TYPE_UNSPECIFIED;
        }

        return switch (feeType) {
            case EXCHANGE_FEE -> TransactionFeeType.EXCHANGE_FEE;
            case TRANSACTION_FEE -> TransactionFeeType.TRANSACTION_FEE;
            case FIXED_FEE -> TransactionFeeType.FIXED_FEE;
            default -> TransactionFeeType.FEE_TYPE_UNSPECIFIED;
        };
    }
}
