package io.wyden.booking.snapshotter.domain.transaction.trade;

import io.wyden.booking.snapshotter.domain.transaction.ExecType;
import io.wyden.booking.snapshotter.domain.transaction.SettlementType;
import io.wyden.booking.snapshotter.domain.transaction.Transaction;
import io.wyden.booking.snapshotter.domain.transaction.TransactionFee;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isNullOrZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isZero;

/**
 * Trade is the base class for all transaction that resulted from an Execution Report.
 * Besides price, quantity, currency the Trade object also contains fields referencing the originating order (intOrderId and extOrderId).
 */
@Entity
@Access(AccessType.FIELD)
public abstract class Trade extends Transaction {

    /**
     * the quantity that was executed
     */
    protected BigDecimal quantity;

    /**
     * the quantity that is still pending
     */
    protected BigDecimal leavesQuantity;

    /**
     * the unit price at which the trade was executed
     */
    protected BigDecimal price;

    /**
     * the transaction / quote currency
     */
    protected String currency;

    /**
     * Internal order identifier generated by the connector, formatted according to external venue requirements.
     * Used for order tracking within the venue-specific connector.
     */
    protected String intOrderId;

    /**
     * External order identifier assigned by the trading venue/exchange.
     * Represents how the order is identified in the external venue's system.
     */
    protected String extOrderId;

    /**
     * Internal OEMS order identifier.
     * Primary identifier for the order within Wyden's OEMS system.
     */
    protected String orderId;

    /**
     * Direct parent order identifier.
     * References the immediate parent order in a chain of orders. For example:
     * - For SOR child orders, references the SOR parent order
     * - For agency street-side orders, references the agency client-side order
     */
    protected String parentOrderId;

    /**
     * The execution ID of the parent/underlying trade that triggered this trade.
     * Used to establish a hierarchical relationship between related trades, particularly
     * useful in scenarios where one trade leads to or triggers another trade.
     */
    protected String rootOrderId;

    /**
     * The execution ID of the parent/underlying trade that triggered this trade.
     * Used to establish a hierarchical relationship between related trades, particularly
     * useful in scenarios where one trade leads to or triggers another trade.
     */
    protected String underlyingExecutionId;

    /**
     * The execution ID of the original/root trade in the execution chain.
     * This ID represents the very first trade that initiated a series of related trades,
     * allowing tracking of the complete execution hierarchy back to its origin.
     * Unlike underlyingExecutionId which points to the immediate parent, this points to the top-most ancestor.
     */
    protected String rootExecutionId;

    /**
     * References the original order ID from the client space (FIX API/REST API/GQL API).
     * Only present for orders that originated from client interactions.
     * This ID is propagated through the entire order chain, allowing tracking back to
     * the initial client request regardless of how many internal orders were created.
     * Unlike rootOrderId which tracks the OEMS space hierarchy, this tracks the client space origin.
     */
    protected String clientRootOrderId;

    /**
     * the (client) portfolio into which the trade is executed
     */
    protected String portfolioId;

    @Column(name = "exec_type")
    @Enumerated(EnumType.STRING)
    protected ExecType execType;

    public Trade(String uuid,
                 String reservationRef,
                 ZonedDateTime dateTime,
                 String executionId,
                 String venueExecutionId,
                 Collection<TransactionFee> fees,
                 String description,
                 boolean isLive,
                 BigDecimal quantity,
                 BigDecimal leavesQuantity,
                 BigDecimal price,
                 String currency,
                 String intOrderId,
                 String extOrderId,
                 String orderId,
                 String parentOrderId,
                 String rootOrderId,
                 String underlyingExecutionId,
                 String rootExecutionId,
                 String clientRootOrderId,
                 String portfolioId,
                 ExecType execType) {
        super(uuid, reservationRef, dateTime, executionId, venueExecutionId, fees, description, isLive, SettlementType.DEFERRED_SETTLEMENT);
        this.quantity = quantity;
        this.leavesQuantity = leavesQuantity;
        this.price = price;
        this.currency = currency;
        this.intOrderId = intOrderId;
        this.extOrderId = extOrderId;
        this.orderId = orderId;
        this.parentOrderId = parentOrderId;
        this.rootOrderId = rootOrderId;
        this.underlyingExecutionId = underlyingExecutionId;
        this.rootExecutionId = rootExecutionId;
        this.clientRootOrderId = clientRootOrderId;
        this.portfolioId = portfolioId;
        this.execType = execType;
    }

    protected Trade() {
        // JPA
    }

    public boolean isClosed() {
        return isNullOrZero(leavesQuantity);
    }

    public boolean isOpen() {
        return !isClosed();
    }

    public boolean isRejectedOrCanceled() {
        return ExecType.REJECTED == execType || ExecType.CANCELED == execType;
    }

    public boolean hasTerminalState() {
        return execType != null && execType.hasTerminalState();
    }

    boolean isFeeOnly() {
        return quantity.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * Amount of the trade excl. fees
     */
    public BigDecimal getGrossValue() {
        if (isNullOrZero(price)) {
            return BigDecimal.ZERO;
        }
        return quantity.multiply(price);
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public BigDecimal getLeavesQuantity() {
        return leavesQuantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public String getCurrency() {
        return currency;
    }

    public String getExtOrderId() {
        return extOrderId;
    }

    public String getIntOrderId() {
        return intOrderId;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getParentOrderId() {
        return parentOrderId;
    }

    public String getRootOrderId() {
        return rootOrderId;
    }

    public String getClientRootOrderId() {
        return clientRootOrderId;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public String getUnderlyingExecutionId() {
        return underlyingExecutionId;
    }

    public String getRootExecutionId() {
        return rootExecutionId;
    }

    public ExecType getExecType() {
        return execType;
    }
}
