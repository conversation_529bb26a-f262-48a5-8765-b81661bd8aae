package io.wyden.booking.wal.infrastructure.rabbit;

import com.google.protobuf.Message;
import io.wyden.booking.wal.application.InboundMessageConsumer;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.booking.command.Command;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class RabbitBinder {

    private static final Logger LOGGER = LoggerFactory.getLogger(InboundMessageConsumer.class);

    public static final List<OemsTargetType> ALLOWED_SOURCE_TYPES = List.of(OemsTargetType.EXTERNAL_VENUE_ACCOUNT, OemsTargetType.BROKER_DESK, OemsTargetType.CLOB);

    private final String queueName;
    private final String consumerName;
    private final RabbitIntegrator rabbitIntegrator;
    private final InboundMessageConsumer inboundMessageConsumer;
    private final RabbitExchange<Message> tradingIngressExchange;
    private final RabbitExchange<Command> bookingCommandExchange;
    private final RabbitQueue<Message> queue;
    private final AtomicBoolean reconciliationCompleted;

    public RabbitBinder(@Value("${rabbitmq.booking.wal.queue}") String queueName,
                        @Value("${spring.application.name}") String consumerName,
                        @Value("${migration.bookingengine.completed}") boolean reconciliationCompleted,
                        RabbitIntegrator rabbitIntegrator,
                        InboundMessageConsumer inboundMessageConsumer) {
        this.reconciliationCompleted = new AtomicBoolean(reconciliationCompleted);
        this.tradingIngressExchange = OemsExchange.Trading.declareIngressExchange(rabbitIntegrator);
        this.bookingCommandExchange = OemsExchange.Booking.declareCommandExchange(rabbitIntegrator);
        this.queueName = queueName;
        this.consumerName = consumerName;
        this.rabbitIntegrator = rabbitIntegrator;
        this.inboundMessageConsumer = inboundMessageConsumer;
        this.queue = declareAndBindQueue();
    }

    public void onReconciliationCompleted() {
        boolean previous = this.reconciliationCompleted.getAndSet(true);
        if (!previous) {
            LOGGER.info("Reconciliation completed, binding realtime events queue to consumer");
            queue.attachConsumer(BookingMessageParser.parser(), inboundMessageConsumer);
        }
    }

    private RabbitQueue<Message> declareAndBindQueue() {
        RabbitQueue<Message> queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .setSingleActiveConsumer(true)
            .setMessageTTL(Duration.ofHours(24))
            .declare();

        bindRequests(queue);
        bindStateMachineExecutionReports(queue);
        bindRiskExecutionReports(queue);
        bindCommands(queue);

        if (reconciliationCompleted.get()) {
            queue.attachConsumer(BookingMessageParser.parser(), inboundMessageConsumer);
        }

        return queue;
    }

    private void bindRequests(RabbitQueue<Message> queue) {
        bindQueue(queue, OemsTargetType.EXTERNAL_VENUE_ACCOUNT.toString(), OemsRequest.OemsPTCStatus.APPROVED.toString());
        bindQueue(queue, OemsTargetType.EXTERNAL_VENUE_ACCOUNT.toString(), OemsRequest.OemsPTCStatus.NOT_REQUIRED.toString());
        bindQueue(queue, OemsTargetType.BROKER_DESK.toString(), OemsRequest.OemsPTCStatus.APPROVED.toString());
        // skip BROKER_DESK + NOT_REQUIRED to avoid observing duplicated request to BrokerConfigService (which won't have counter-portfolio resolved yet)
        bindQueue(queue, OemsTargetType.CLOB.toString(), OemsRequest.OemsPTCStatus.APPROVED.toString());
        bindQueue(queue, OemsTargetType.CLOB.toString(), OemsRequest.OemsPTCStatus.NOT_REQUIRED.toString());
    }

    private void bindQueue(RabbitQueue<Message> queue, String targetType, String ptc) {
        Map<String, Object> ptcApprovedHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
            OemsHeader.PTC.getHeaderName(), ptc,
            OemsHeader.TARGET_TYPE.getHeaderName(), targetType
        );

        LOGGER.info("Binding exchange {} and queue {} with headers {}", tradingIngressExchange.getName(), queue.getName(), ptcApprovedHeaders);
        queue.bindWithHeaders(tradingIngressExchange, MatchingCondition.ALL, ptcApprovedHeaders);
    }

    private void bindCommands(RabbitQueue queue) {
        Map<String, Object> headers = Map.of();
        LOGGER.info("Binding exchange {} and queue {} with headers {}", bookingCommandExchange, queue.getName(), headers);
        queue.bindWithHeaders(bookingCommandExchange, MatchingCondition.ALL, headers);
    }

    private void bindStateMachineExecutionReports(RabbitQueue<Message> queue) {
        for (OemsTargetType sourceType : ALLOWED_SOURCE_TYPES) {
            bindQueue(queue, sourceType.toString(), tradingIngressExchange);
        }
    }

    private void bindRiskExecutionReports(RabbitQueue<Message> queue) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.OEMS_RESPONSE_TYPE.getHeaderName(), OemsResponse.OemsResponseType.EXECUTION_REPORT.toString(),
            OemsHeader.SOURCE.getHeaderName(), "risk"
        );
        LOGGER.info("Binding exchange {} and queue {} with headers {}", tradingIngressExchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(tradingIngressExchange, MatchingCondition.ALL, headers);
    }

    private void bindQueue(RabbitQueue<Message> queue, String sourceType, RabbitExchange<Message> exchange) {
        Map<String, Object> headers = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName(),
            OemsHeader.OEMS_RESPONSE_TYPE.getHeaderName(), OemsResponse.OemsResponseType.EXECUTION_REPORT.toString(),
            OemsHeader.SOURCE_TYPE.getHeaderName(), sourceType
        );

        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange, queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }

}
