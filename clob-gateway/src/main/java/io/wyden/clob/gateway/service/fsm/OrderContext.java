package io.wyden.clob.gateway.service.fsm;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.clob.gateway.model.ClobOrderState;
import io.wyden.clob.gateway.model.CounterOrderForMatch;
import io.wyden.clob.gateway.model.PendingHedge;
import io.wyden.clob.gateway.model.PlaceOrderCommand;
import io.wyden.clob.gateway.service.aeron.model.MatchingEngineEvent;
import io.wyden.clob.gateway.service.aeron.model.ProtoMapper;
import io.wyden.clob.gateway.service.aeron.outbound.ExchangeCoreClient;
import io.wyden.clob.gateway.service.audit.MatchAuditContext;
import io.wyden.clob.gateway.service.config.QuotingConfigCacheFacade;
import io.wyden.clob.gateway.service.marketdata.MarketDataEmitter;
import io.wyden.clob.gateway.service.marketdata.MarketDataService;
import io.wyden.clob.gateway.service.oems.Trade;
import io.wyden.clob.gateway.service.oems.outbound.OemsResponseFactory;
import io.wyden.clob.gateway.service.referencedata.InstrumentsRepository;
import io.wyden.clob.gateway.service.tracking.FailureNonRecoverableException;
import io.wyden.clob.gateway.service.tracking.FailureRequeueException;
import io.wyden.clob.gateway.service.tracking.OrderCache;
import io.wyden.clob.gateway.service.tracking.OrderIdentificationTracker;
import io.wyden.clob.gateway.service.tracking.OrderUuidResolver;
import io.wyden.clob.gateway.service.tracking.OrderValidator;
import io.wyden.clob.gateway.service.tracking.TradeContext;
import io.wyden.clob.gateway.service.tracking.TradeParticipant;
import io.wyden.clob.gateway.utils.MathUtils;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.oems.OemsLiquidityIndicator;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.rate.client.MarketDataCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.sbe.OrderAction;
import io.wyden.sbe.OrderType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.COUNT_MATCH_EVENT;
import static io.wyden.clob.gateway.infrastructure.telemetry.TelemetryConfiguration.MATCHING_ENGINE_SYMBOL;
import static io.wyden.clob.gateway.service.fsm.StatusNew.reduceOrder;
import static io.wyden.clob.gateway.utils.BigDecimalUtils.bd;
import static io.wyden.clob.gateway.utils.TradingMessageUtils.isCashOrder;
import static io.wyden.cloudutils.tools.ProtobufUtils.shortDebugString;
import static io.wyden.published.oems.OemsLiquidityIndicator.TAKER;
import static org.apache.commons.lang3.StringUtils.firstNonBlank;


// TODO: Duplicate messages

class OrderContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderContext.class);

    private final OrderService orderService;
    private final MeterRegistry meterRegistry;
    private final Tracing otlTracing;
    private OrderState orderState;

    OrderContext(OrderService orderService,
                 Telemetry telemetry) {
        this.orderService = orderService;
        this.meterRegistry = telemetry.getMeterRegistry();
        this.otlTracing = telemetry.getTracing();
    }

    void onOemsRequest(OemsRequest request) {
        OemsRequest.OemsRequestType requestType = request.getRequestType();
        Span.current().addEvent("Processing: " + requestType);
        LOGGER.info("Consuming OemsRequest {}: {}", requestType, request);
        switch (requestType) {
            case ORDER_SINGLE -> onNewOrder(request);
            case CANCEL -> onCancel(request);
            default -> throw new FailureRequeueException("Unknown OemsRequest requestType=" + request.getRequestTypeValue());
        }
    }

    private void onNewOrder(OemsRequest request) {
        try {
            if (orderService.isAeronHealthy()) {
                getOrderValidator().validateOemsOrder(request);
                Portfolio portfolio = orderService.getPortfoliosRepository().get(request.getPortfolioId());
                Portfolio clientPortfolio = orderService.getPortfoliosRepository().get(request.getClientPortfolioId());
                orderState = new OrderState(request, portfolio, clientPortfolio);
                orderState.getOrderStatus().onNewOrder(this, request, orderService.getClobRequestFactory());
                updateClobOrderState();
            } else {
                handleNewOrderRejection(request, "CLOB not available");
            }
        } catch (OrderCache.DuplicateOrderState ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleDuplicateOrderState(request, ex);
            updateClobOrderState();
        } catch (FailureRequeueException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleNewOrderRejection(request, ex.getMessage());
            updateClobOrderState();
        }
    }

    private void onCancel(OemsRequest request) {
        try {
            getOrderValidator().validateOemsCancel(request);
            String orderId = request.getOrderId();
            getOrderIdentificationTracker().find(orderId).ifPresentOrElse(clobOrderState -> {
                orderState = new OrderState(clobOrderState);
                orderState.getOrderStatus().onCancel(this, request);
                updateClobOrderState();
            }, () -> {
                throw new FailureRequeueException("ClobOrderState not found for orderId=" + orderId);
            });
        } catch (FailureRequeueException ex) {
            throw ex;
        } catch (Exception ex) {
            LOGGER.error("Exception: ", ex);
            Span.current().recordException(ex);
            handleOtherCancelException(request, ex);
            updateClobOrderState();
        }
    }

    public void onPlaceOrder(MatchingEngineEvent.PlaceOrder placeOrder, Instrument instrument) {
        String orderUuid = OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), placeOrder);

        ClobOrderState clobOrderState = getOrderIdentificationTracker().find(orderUuid)
            .orElseThrow(() -> new FailureNonRecoverableException("OrderState not found for orderUuid=" + orderUuid));

        orderState = new OrderState(clobOrderState);

        if (placeOrder.success()) {
            orderState.getOrderStatus().onPlaceOrder(this, placeOrder, instrument);
        } else {
            orderState.getOrderStatus().onRejected(this, placeOrder);
        }

        updateClobOrderState();
    }

    public void onTradeEvent(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, Instrument instrument) {
        TradeParticipant taker = resolveTradeParticipant(takerTradeEvent.takerUid(), instrument);
        MatchAuditContext matchAuditContext = new MatchAuditContext(takerTradeEvent);
        int symbol = takerTradeEvent.symbol();
        String instrumentId = instrument.getInstrumentIdentifiers().getInstrumentId();

        for (MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade : takerTradeEvent.makerTrades()) {
            try (var ignored2 = otlTracing.createSpan("tradeevent.handle.makertrade", SpanKind.INTERNAL)) {
                TradeParticipant maker = resolveTradeParticipant(makerTrade.makerUid(), instrument);
                TradeContext tradeContext = new TradeContext(maker, taker);

                String lastPrice = MathUtils.convertPrice(makerTrade.price(), instrument).toPlainString();
                String lastQty = MathUtils.convertVolume(makerTrade.volume(), instrument).toPlainString();

                if (tradeContext.isClientToClientMatch()) {
                    String makerOrderUuid = OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), makerTrade);
                    String takerOrderUuid = OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), takerTradeEvent);
                    LOGGER.info("Internal match: maker={orderId={}, portfolio={}}, taker={orderId={}, portfolio={}}, matchId={}",
                        makerOrderUuid, tradeContext.maker().getId(), takerOrderUuid, tradeContext.taker().getId(), takerTradeEvent.matchId());
                    AtomicReference<OemsRequest> takerOrderOemsRequest = new AtomicReference<>(OemsRequest.newBuilder().build());
                    try (var ignored3 = otlTracing.createSpan("tradeevent.handle.internal.taker", SpanKind.INTERNAL)) {
                        getOrderIdentificationTracker().find(takerOrderUuid).ifPresentOrElse(clobOrderState -> {
                            orderState = new OrderState(clobOrderState);
                            orderState.getOrderStatus().onTrade(this, wrapClientTradeTaker(takerTradeEvent, lastPrice, lastQty, instrument, orderService.getClobVenueAccountId()));
                            updateClobOrderState();
                            takerOrderOemsRequest.set(orderState.getOemsRequest());
                        }, () -> {
                            LOGGER.warn("OrderState not found for orderId={}", takerTradeEvent.takerOrderId());
                        });
                    }

                    matchAuditContext.setTaker(new MatchAuditContext.Counterparty(
                        taker,
                        MathUtils.convertVolume(takerTradeEvent.totalVolume(), instrument).toPlainString(),
                        MathUtils.convertPrice(takerTradeEvent.price(), instrument).toPlainString(),
                        takerOrderOemsRequest.get().getOrderId(),
                        takerOrderOemsRequest.get().getRootOrderId()
                    ));

                    AtomicReference<OemsRequest> makerOrderOemsRequest = new AtomicReference<>(OemsRequest.newBuilder().build());
                    try (var ignored4 = otlTracing.createSpan("tradeevent.handle.internal.maker", SpanKind.INTERNAL)) {
                        getOrderIdentificationTracker().find(makerOrderUuid).ifPresentOrElse(clobOrderState -> {
                            orderState = new OrderState(clobOrderState);
                            orderState.getOrderStatus().onTrade(this, wrapClientTradeMaker(takerTradeEvent, makerTrade, lastPrice, lastQty, instrument, orderService.getClobVenueAccountId()));
                            updateClobOrderState();
                            makerOrderOemsRequest.set(orderState.getOemsRequest());
                        }, () -> {
                            LOGGER.warn("OrderState not found for orderId={}", makerTrade.makerOrderId());
                        });
                    }

                    matchAuditContext.addMaker(new MatchAuditContext.Counterparty(
                        maker,
                        lastQty,
                        lastPrice,
                        makerOrderOemsRequest.get().getOrderId(),
                        makerOrderOemsRequest.get().getRootOrderId()
                    ));
                    updateMetrics(symbol, instrumentId, "internal");
                } else if (tradeContext.isClientToQuoteMatch()) {
                    TradeParticipant clientTradeParticipant = tradeContext.isClientTaker() ? tradeContext.taker() : tradeContext.maker();
                    TradeParticipant quotingTradeParticipant = tradeContext.isClientTaker() ? tradeContext.maker() : tradeContext.taker();
                    OemsLiquidityIndicator clientLiquidityIndicator = tradeContext.isClientTaker() ? TAKER : OemsLiquidityIndicator.MAKER;
                    String clientOrderUuid = clientLiquidityIndicator == OemsLiquidityIndicator.MAKER ? OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), makerTrade) : OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), takerTradeEvent);;
                    LOGGER.info("External match: client={orderId={}, portfolio={}, liquidityIndicator={}}, quotingEnginePortfolio={}, matchId={}",
                        clientOrderUuid, clientTradeParticipant.getId(), clientLiquidityIndicator, quotingTradeParticipant.getId(), takerTradeEvent.matchId());
                    long matchId = takerTradeEvent.matchId();
                    try (var ignored4 = otlTracing.createSpan("tradeevent.handle.external", SpanKind.INTERNAL)) {
                        getOrderIdentificationTracker().find(clientOrderUuid).ifPresentOrElse(clobOrderState -> {
                            orderState = new OrderState(clobOrderState);
                            orderState.getOrderStatus().onExternalMatch(this, takerTradeEvent, makerTrade, instrument, clientLiquidityIndicator);
                            updateClobOrderState();

                            CounterOrderForMatch counterOrderForMatch = Optional.ofNullable(orderState.getCounterOrderForMatches(matchId))
                                .or(() -> Optional.ofNullable(orderState.getCounterOrderIdForMatch(matchId))
                                    .map(counterOrderId -> CounterOrderForMatch.newBuilder().setOrderId(counterOrderId).build()))
                                .orElse(CounterOrderForMatch.newBuilder().build());

                            matchAuditContext.setTaker(new MatchAuditContext.Counterparty(
                                taker,
                                MathUtils.convertVolume(takerTradeEvent.totalVolume(), instrument).toPlainString(),
                                MathUtils.convertPrice(takerTradeEvent.price(), instrument).toPlainString(),
                                tradeContext.isClientTaker() ? orderState.getOemsRequest().getOrderId() : counterOrderForMatch.getOrderId(),
                                tradeContext.isClientTaker() ? orderState.getOemsRequest().getRootOrderId() : counterOrderForMatch.getRootOrderId(),
                                tradeContext.isClientTaker() ? "" : getCounterPortfolio(takerTradeEvent.takerUid())
                            ));

                            matchAuditContext.addMaker(new MatchAuditContext.Counterparty(
                                maker,
                                lastQty,
                                lastPrice,
                                tradeContext.isClientMaker() ? orderState.getOemsRequest().getOrderId() : counterOrderForMatch.getOrderId(),
                                tradeContext.isClientMaker() ? orderState.getOemsRequest().getRootOrderId() : counterOrderForMatch.getRootOrderId(),
                                tradeContext.isClientMaker() ? "" : getCounterPortfolio(makerTrade.makerUid())
                            ));

                            matchAuditContext.setExternalVenueQuotes(orderState.getExternalVenuesQuotesForMatch(matchId));

                            if (orderState.getFxQuotesForMatch(matchId) != null) {
                                matchAuditContext.setFxQuotes(orderState.getFxQuotesForMatch(matchId));
                            }
                        }, () -> {
                            LOGGER.warn("OrderState not found for clientOrderId={}", clientOrderUuid);
                        });
                        updateMetrics(symbol, instrumentId, "external");
                    }
                } else {
                    updateMetrics(symbol, instrumentId, "unhandled");
                    LOGGER.debug("Unhandled match: {}, trade={} from tradeEvent={}", tradeContext, makerTrade, takerTradeEvent);
                }
            }
        }
        orderService.getAuditService().sendMatchAuditEvent(matchAuditContext, instrument);
    }

    private void updateMetrics(int symbol, String instrumentId, String matchType) {
        try {
            this.meterRegistry.counter(COUNT_MATCH_EVENT,
                "instrumentId", instrumentId,
                MATCHING_ENGINE_SYMBOL, String.valueOf(symbol),
                "matchType", matchType
            ).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Trade wrapClientTradeMaker(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade, String lastPrice, String lastQty, Instrument instrument, String venueAccountId) {
        return new Trade(
            takerTradeEvent.matchId(),
            makerTrade.makerOrderId(),
            OemsLiquidityIndicator.MAKER,
            lastPrice,
            lastQty,
            takerTradeEvent.timestamp(),
            instrument.getInstrumentIdentifiers().getInstrumentId(),
            takerTradeEvent.symbol(),
            venueAccountId,
            String.valueOf(makerTrade.makerTradeId())
        );
    }

    private Trade wrapClientTradeTaker(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, String lastPrice, String lastQty, Instrument instrument, String venueAccountId) {
        return new Trade(
            takerTradeEvent.matchId(),
            takerTradeEvent.takerOrderId(),
            TAKER,
            lastPrice,
            lastQty,
            takerTradeEvent.timestamp(),
            instrument.getInstrumentIdentifiers().getInstrumentId(),
            takerTradeEvent.symbol(),
            venueAccountId,
            String.valueOf(takerTradeEvent.takerTradeId())
        );
    }

    private Trade wrapClientTradeMaker(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade, String lastPrice, String lastQty, OemsResponse oemsResponse) {
        return new Trade(
            takerTradeEvent.matchId(),
            makerTrade.makerOrderId(),
            OemsLiquidityIndicator.MAKER,
            lastPrice,
            lastQty,
            takerTradeEvent.timestamp(),
            oemsResponse.getRootExecution().getInstrumentId(),
            takerTradeEvent.symbol(),
            oemsResponse.getRootExecution().getVenueAccount(),
            oemsResponse.getVenueExecutionId()
        );
    }

    private Trade wrapClientTradeTaker(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, String lastPrice, String lastQty, OemsResponse oemsResponse) {
        return new Trade(
            takerTradeEvent.matchId(),
            takerTradeEvent.takerOrderId(),
            TAKER,
            lastPrice,
            lastQty,
            takerTradeEvent.timestamp(),
            oemsResponse.getRootExecution().getInstrumentId(),
            takerTradeEvent.symbol(),
            oemsResponse.getRootExecution().getVenueAccount(),
            oemsResponse.getVenueExecutionId()
        );
    }

    public void onOemsResponse(OemsResponse oemsResponse) {
        getOrderIdentificationTracker().find(oemsResponse.getParentOrderId()).ifPresentOrElse(clobOrderState -> {
            orderState = new OrderState(clobOrderState);

            PendingHedge pendingHedge = orderState.getPendingHedge(oemsResponse.getOrderId());
            if (pendingHedge == null) {
                throw new FailureNonRecoverableException("PendingHedge not found for OemsResponse orderId=" + oemsResponse.getOrderId());
            }

            LOGGER.info("Received OemsResponse {} for hedging matchId={}, takerOrderId={}, makerOrderId={}: {}",
                oemsResponse.getOrderStatus(),
                pendingHedge.getTakerTradeEvent().getMatchId(),
                pendingHedge.getTakerTradeEvent().getTakerOrderId(),
                pendingHedge.getMakerTrade().getMakerOrderId(),
                oemsResponse
            );

            Instrument instrument = orderService.getInstrumentsTracker().getInstrument(pendingHedge.getTakerTradeEvent().getSymbol());
            if (instrument == null) {
                throw new FailureNonRecoverableException("SymbolSpecification not found for symbol=" + pendingHedge.getTakerTradeEvent().getSymbol());
            }

            if (oemsResponse.getResponseType() != OemsResponse.OemsResponseType.EXECUTION_REPORT) {
                throw new FailureNonRecoverableException("Unexpected response type=" + oemsResponse.getResponseType());
            }

            switch (oemsResponse.getOrderStatus()) {
                case STATUS_NEW, STATUS_PARTIALLY_FILLED -> LOGGER.debug("Received OemsResponse with status {}, ignoring {}", oemsResponse.getOrderStatus(), shortDebugString(oemsResponse));
                case STATUS_FILLED -> handleQuotingOrderFilled(oemsResponse, pendingHedge, instrument);
                case STATUS_REJECTED, STATUS_CANCELED -> handleQuotingOrderRejected(oemsResponse, pendingHedge, instrument);
                default -> throw new FailureNonRecoverableException("Unexpected orderStatus=" + oemsResponse.getOrderStatus());
            }
        }, () -> {
            throw new FailureNonRecoverableException("OrderState not found for orderId=" + oemsResponse.getParentOrderId());
        });
    }

    private void handleQuotingOrderFilled(OemsResponse oemsResponse, PendingHedge pendingHedge, Instrument instrument) {
        MatchingEngineEvent.TakerTradeEvent takerTradeEvent = ProtoMapper.fromProto(pendingHedge.getTakerTradeEvent());
        MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade = ProtoMapper.fromProto(pendingHedge.getMakerTrade());

        TradeContext tradeContext = resolveTradeContext(takerTradeEvent, makerTrade, instrument);

        BigDecimal lastPrice = MathUtils.convertPrice(makerTrade.price(), instrument);
        BigDecimal cumQty = bd(oemsResponse.getCumQty()); // intermediate partial fills are ignored

        Trade trade = tradeContext.isClientTaker() ?
            wrapClientTradeTaker(takerTradeEvent, lastPrice.toPlainString(), cumQty.toPlainString(), oemsResponse) :
            wrapClientTradeMaker(takerTradeEvent, makerTrade, lastPrice.toPlainString(), cumQty.toPlainString(), oemsResponse);

        String requestQty = pendingHedge.getRequest().getQuantity();
        BigDecimal responseQty = bd(oemsResponse.getOrderQty());
        BigDecimal reducedQty = bd(requestQty).subtract(responseQty);
        if (isCashOrder(orderState.getOemsRequest())) {
            reducedQty = reducedQty.multiply(lastPrice);
        }
        if (reducedQty.compareTo(BigDecimal.ZERO) > 0) {
            reduceOrder(this, trade, reducedQty, lastPrice, "Unsolicited reduce, hedging venue qty constraints");
        }

        orderState.removePendingHedge(oemsResponse.getOrderId());

        orderState.getOrderStatus().onTrade(this, trade);

        updateClobOrderState();
    }

    void handleQuotingOrderRejected(OemsResponse oemsResponse, PendingHedge pendingHedge, Instrument instrument) {
        MatchingEngineEvent.TakerTradeEvent takerTradeEvent = ProtoMapper.fromProto(pendingHedge.getTakerTradeEvent());
        MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade = ProtoMapper.fromProto(pendingHedge.getMakerTrade());

        TradeContext tradeContext = resolveTradeContext(takerTradeEvent, makerTrade, instrument);

        String lastPrice = MathUtils.convertPrice(makerTrade.price(), instrument).toPlainString();
        String lastQty = MathUtils.convertVolume(makerTrade.volume(), instrument).toPlainString();

        if (tradeContext.isClientTaker()) {
            orderState.getOrderStatus().onHedgingOrderRejected(this, wrapClientTradeTaker(takerTradeEvent, lastPrice, lastQty, oemsResponse));
        } else if (tradeContext.isClientMaker()) {
            orderState.getOrderStatus().onHedgingOrderRejected(this, wrapClientTradeMaker(takerTradeEvent, makerTrade, lastPrice, lastQty, oemsResponse));
        }

        orderState.removePendingHedge(oemsResponse.getOrderId());
        updateClobOrderState();
    }

    public void onCancelOrder(MatchingEngineEvent.CancelOrder cancelOrder) {
        String orderUuid = OrderUuidResolver.resolveOrderUuid(getOrderIdentificationTracker(), cancelOrder);
        ClobOrderState clobOrderState = getOrderIdentificationTracker().find(orderUuid)
            .orElseThrow(() -> new FailureNonRecoverableException("OrderState not found for orderId=" + cancelOrder.orderId()));
        orderState = new OrderState(clobOrderState);

        if (cancelOrder.success()) {
            if (cancelOrder.orderExpired()) {
                orderState.getOrderStatus().onExpired(this);
            } else {
                orderState.getOrderStatus().onCancelled(this, cancelOrder.orderId());
            }
        } else {
            orderState.getOrderStatus().onCancelReject(this, cancelOrder.orderId(), cancelOrder.result());
        }

        updateClobOrderState();
    }

    public void cancelExpiredOrder(ClobOrderState clobOrderState) {
        orderState = new OrderState(clobOrderState);
        orderState.getOrderStatus().onExpired(this);
        updateClobOrderState();
    }

    OrderCache getOrderCache() {
        return orderService.getOrderCache();
    }

    OrderIdentificationTracker getOrderIdentificationTracker() {
        return orderService.getOrderIdentificationTracker();
    }

    OrderValidator getOrderValidator() {
        return orderService.getOrderValidator();
    }

    TradeExecutor getTradeExecutor() {
        return orderService.getTradeExecutor();
    }

    ExchangeCoreClient.ExchangeCoreCommandResult emit(PlaceOrderCommand placeOrderCommand, String instrumentId) {
        ExchangeCoreClient.ExchangeCoreCommandResult exchangeCoreCommandResult = orderService.getExchangeCoreClient().sendPlaceOrderCommand(
            placeOrderCommand.getSymbol(),
            placeOrderCommand.getUid(),
            placeOrderCommand.getOrderId(),
            UUID.fromString(placeOrderCommand.getOrderUuid()),
            placeOrderCommand.getPrice(),
            placeOrderCommand.getSize(),
            placeOrderCommand.getAmount(),
            switch (placeOrderCommand.getAction()) {
                case ASK -> OrderAction.ASK;
                case BID -> OrderAction.BID;
                case ORDER_ACTION_UNSPECIFIED, UNRECOGNIZED -> OrderAction.NULL_VAL;
            },
            switch (placeOrderCommand.getOrderType()) {
                case GTC -> OrderType.GTC;
                case IOC -> OrderType.IOC;
                case FOK -> OrderType.FOK;
                case GTD -> OrderType.GTD;
                case ORDER_TYPE_UNSPECIFIED, UNRECOGNIZED -> OrderType.NULL_VAL;
            },
            placeOrderCommand.getExpiry(),
            instrumentId
        );

        return exchangeCoreCommandResult;
    }

    void emitCancelOrderCommand(OrderState state) {
        Instrument instrument = orderService.getInstrumentsRepository().get(state.getOemsRequest().getInstrumentId());
        int symbol = instrument.getInstrumentIdentifiers().getMatchingEngineId();
        orderService.getExchangeCoreClient().sendCancelOrderCommand(
            symbol,
            state.getClientPortfolio().getMatchingEngineUid(),
            orderState.getPlaceOrderCommand().getOrderId()
        );
    }

    void emit(OemsRequest oemsRequest) {
        orderService.getOemsRequestEmitter().emit(oemsRequest);
    }

    void emit(OemsResponse oemsResponse) {
        orderService.getOemsResponseEmitter().emit(oemsResponse);
    }

    OrderState getOrderState() {
        return orderState;
    }

    void addClobOrderState() {
        ClobOrderState clobOrderState;
        if (orderState != null && orderState.isDirty()) {
            clobOrderState = orderState.toClobOrderState();
            orderService.getOrderCache().add(clobOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping add");
        }
    }

    void updateClobOrderState() {
        ClobOrderState clobOrderState;
        if (orderState != null && orderState.isDirty()) {
            clobOrderState = orderState.toClobOrderState();
            orderService.getOrderCache().update(clobOrderState);
            orderState.incSequenceNum();
            orderState.setDirty(false);
        } else {
            LOGGER.debug("OrderState clean - skipping update");
        }
    }

    private void handleDuplicateOrderState(OemsRequest request, OrderCache.DuplicateOrderState ex) {
        getOrderIdentificationTracker().find(request.getOrderId()).ifPresentOrElse(clobOrderState -> {
            OrderState state = new OrderState(clobOrderState);
            OemsResponse reject = OemsResponseFactory.createRejectAsDuplicate(state, ex.getMessage());
            emit(reject);
        }, () -> {
            throw new FailureRequeueException("Inconsistent cache state. Re-queueing...");
        });
    }

    private void handleNewOrderRejection(OemsRequest request, String reason) {
        OemsResponse reject = OemsResponseFactory.createRejectWithRegistrationError(request, reason);
        emit(reject);
    }

    private void handleOtherCancelException(OemsRequest request, Exception ex) {
        OemsResponse reject = OemsResponseFactory.createCancelRejectGenericError(request, ex.getMessage());
        emit(reject);
    }

    public QuotingConfigCacheFacade getQuotingConfigCacheFacade() {
        return orderService.getQuotingConfigCacheFacade();
    }

    public InstrumentsRepository getInstrumentsRepository() {
        return orderService.getInstrumentsRepository();
    }

    public VenueAccountCacheFacade getVenueAccountCacheFacade() {
        return orderService.getVenueAccountCacheFacade();
    }

    public MarketDataCacheFacade getMarketDataCacheFacade() {
        return orderService.getMarketDataCacheFacade();
    }

    private TradeContext resolveTradeContext(MatchingEngineEvent.TakerTradeEvent takerTradeEvent, MatchingEngineEvent.TakerTradeEvent.MakerTrade makerTrade, Instrument instrument) {
        TradeParticipant taker = resolveTradeParticipant(takerTradeEvent.takerUid(), instrument);
        TradeParticipant maker = resolveTradeParticipant(makerTrade.makerUid(), instrument);
        return new TradeContext(maker, taker);
    }

    private TradeParticipant resolveTradeParticipant(long uid, Instrument instrument) {
        try (var ignored2 = otlTracing.createSpan("tradeevent.resolveparticipant", SpanKind.INTERNAL)) {
            QuotingConfig quotingConfig = orderService.getQuotingConfigCacheFacade().find(uid);
            Portfolio portfolio = orderService.getPortfoliosRepository().get(uid);
            if (quotingConfig != null && portfolio != null) {
                return new TradeParticipant.QuotingEngine(portfolio, quotingConfig, instrument);
            }

            if (quotingConfig == null && portfolio != null) {
                return new TradeParticipant.Client(portfolio);
            }

            return new TradeParticipant.Unresolved(uid);
        }
    }

    private String getCounterPortfolio(long uid) {
        QuotingConfig quotingConfig = orderService.getQuotingConfigCacheFacade().find(uid);
        if (quotingConfig != null) {
            return quotingConfig.getNostroPortfolio();
        } else {
            throw new FailureNonRecoverableException("Unable to resolve quoting config for uid %d".formatted(uid));
        }
    }

    public MarketDataService getMarketDataService() {
        return orderService.getMarketDataService();
    }

    public MarketDataEmitter getMarketDataEmitter() {
        return orderService.getMarketDataEmitter();
    }
}
